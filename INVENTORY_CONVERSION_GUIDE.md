# Inventory Conversion Feature Guide

This document provides comprehensive information about the enhanced inventory conversion functionality in the Coffee Shop POS system, including raw material conversion to intermediate products with proper stock tracking and validation.

## 🚀 Overview

The inventory conversion feature allows you to:
- Convert raw materials (like coffee powder) to intermediate products (like decoction)
- Track both raw and converted stock separately
- Maintain existing recipe flows
- Apply proper validation and error handling
- Monitor conversion operations with detailed logging

## 📋 Key Features

### Core Conversion Capabilities

| Feature | Description | Example |
|---------|-------------|---------|
| **Raw to Intermediate** | Convert raw materials to usable products | Coffee Powder → Coffee Decoction |
| **Conversion Ratios** | Define precise conversion formulas | 100g powder → 1L decoction (ratio: 0.01) |
| **Stock Tracking** | Separate tracking for raw and converted stock | Raw: 2kg, Converted: 15L |
| **Validation** | Comprehensive input and operation validation | Amount checks, ratio validation |
| **Error Handling** | Detailed error messages and recovery | Insufficient stock warnings |

### Supported Conversion Scenarios

1. **Coffee Production**
   - Coffee Powder → Coffee Decoction
   - Tea Leaves → Tea Decoction
   - Cocoa Powder → Hot Chocolate Base

2. **Dairy Processing**
   - Milk Powder → Liquid Milk
   - Cream Powder → Whipped Cream

3. **Syrup Production**
   - Sugar + Water → Simple Syrup
   - Flavor Concentrates → Flavored Syrups

## 🛠️ How to Use

### Setting Up Convertible Inventory

1. **Navigate to Inventory Management**
   - Open the app drawer
   - Select "Inventory"
   - Click "Add New Inventory"

2. **Configure Basic Information**
   - Enter product name (e.g., "Premium Coffee Powder")
   - Set quantity and unit (e.g., "2000 gram")
   - Select appropriate unit from dropdown

3. **Enable Conversion**
   - Check the "Convertible" checkbox
   - Fill in conversion details:
     - **Output Item Name**: Name of converted product
     - **Output Quantity**: Expected output amount
     - **Output Unit**: Unit for converted product
     - **Conversion Ratio**: Mathematical conversion factor

4. **Save Configuration**
   - Click "Save" to create the convertible inventory item
   - The system validates all conversion parameters

### Performing Conversions

1. **Access Conversion Interface**
   - Find the convertible inventory item in the list
   - Look for the green "Convert" icon (transform symbol)
   - Click the convert button

2. **Enter Conversion Details**
   - View available raw stock
   - See conversion ratio information
   - Enter amount to convert
   - Review conversion preview

3. **Execute Conversion**
   - Click "Convert" to perform the operation
   - System validates sufficient stock
   - Updates both raw and converted quantities
   - Shows success/error feedback

### Conversion Formula Examples

```
Coffee Powder Conversion:
- Raw: 1000g Coffee Powder
- Ratio: 0.01 (1g → 0.01L)
- Convert: 500g
- Result: 500g raw remaining, 5L decoction produced

Milk Powder Conversion:
- Raw: 2000g Milk Powder  
- Ratio: 0.008 (125g → 1L)
- Convert: 1000g
- Result: 1000g raw remaining, 8L liquid milk produced
```

## 🔧 Technical Implementation

### Core Classes

1. **InventoryDto**: Enhanced with conversion fields
   - `isConvertible`: Boolean flag for conversion capability
   - `outputItemName`: Name of converted product
   - `conversionRatio`: Mathematical conversion factor
   - `convertedStock`: Current converted stock amount

2. **InventoryConversionService**: Validation and calculation service
   - `validateConversion()`: Pre-conversion validation
   - `calculateConversion()`: Conversion mathematics
   - `validateConversionSetup()`: Setup field validation

3. **InventoryScreenProvider**: Enhanced with conversion logic
   - `convertInventory()`: Main conversion method
   - `getConversionValidationErrors()`: Validation feedback

### Database Schema

```sql
-- Convertible inventory columns
ALTER TABLE inventory ADD COLUMN isConvertible INTEGER DEFAULT 0;
ALTER TABLE inventory ADD COLUMN outputItemName TEXT DEFAULT NULL;
ALTER TABLE inventory ADD COLUMN outputQuantity TEXT DEFAULT NULL;
ALTER TABLE inventory ADD COLUMN outputUnit TEXT DEFAULT NULL;
ALTER TABLE inventory ADD COLUMN conversionRatio TEXT DEFAULT NULL;
ALTER TABLE inventory ADD COLUMN convertedStock TEXT DEFAULT '0';
```

### Validation Rules

1. **Conversion Setup Validation**
   - Output item name: Required, non-empty
   - Output quantity: Required, positive number
   - Output unit: Required, valid unit selection
   - Conversion ratio: Required, positive number

2. **Conversion Operation Validation**
   - Amount to convert: Must be positive
   - Stock availability: Must have sufficient raw stock
   - Conversion ratio: Must be valid positive number
   - Convertible flag: Item must be marked as convertible

## 📊 Stock Tracking

### Dual Stock System

The system maintains separate tracking for:

1. **Raw Stock** (`inventoryQunatity`)
   - Original raw material quantity
   - Decreases when conversions are performed
   - Used for recipe calculations

2. **Converted Stock** (`convertedStock`)
   - Quantity of converted/processed product
   - Increases when conversions are performed
   - Available for direct use in products

### Stock Display

```
Coffee Powder Inventory:
├── Raw Stock: 1,500g (available for conversion)
├── Converted Stock: 12L Coffee Decoction (ready to use)
├── Conversion Ratio: 1g → 0.01L
└── Max Convertible: 15L (from remaining raw stock)
```

## 🧪 Testing

The conversion feature includes comprehensive tests:

```bash
# Run all conversion tests
flutter test test/inventory_conversion_test.dart

# Test specific functionality
flutter test test/inventory_conversion_test.dart --name "conversion validation"
```

### Test Coverage
- ✅ InventoryDto conversion calculations
- ✅ Conversion service validation
- ✅ Error handling scenarios
- ✅ Edge cases and null safety
- ✅ Serialization/deserialization
- ✅ Real-world conversion scenarios

## 🔒 Error Handling

### Common Error Scenarios

| Error Code | Description | User Message | Resolution |
|------------|-------------|--------------|------------|
| `notConvertible` | Item not marked as convertible | "This inventory item is not convertible" | Enable conversion in item settings |
| `invalidAmount` | Zero or negative conversion amount | "Conversion amount must be greater than zero" | Enter positive amount |
| `insufficientStock` | Not enough raw stock | "Insufficient raw stock. Available: X units" | Reduce conversion amount or restock |
| `invalidRatio` | Invalid conversion ratio | "Invalid conversion ratio" | Check conversion setup |
| `databaseError` | Database operation failed | "Failed to save conversion to database" | Check database connectivity |

### Error Recovery

1. **Validation Errors**: Show specific field errors with correction guidance
2. **Stock Errors**: Display available stock and suggest valid amounts
3. **System Errors**: Log detailed information and show user-friendly messages
4. **Recovery Actions**: Provide clear next steps for error resolution

## 📈 Best Practices

### Conversion Setup
1. Use descriptive names for converted products
2. Set realistic conversion ratios based on actual yields
3. Choose appropriate units for both raw and converted products
4. Test conversions with small amounts first

### Stock Management
1. Monitor conversion efficiency regularly
2. Adjust ratios based on actual production results
3. Maintain adequate raw stock levels
4. Track conversion patterns for inventory planning

### Quality Control
1. Validate conversion ratios against real-world results
2. Regular audits of converted vs. raw stock
3. Monitor for conversion errors or inconsistencies
4. Update ratios when recipes or processes change

## 🔧 Configuration

### Conversion Ratio Calculation

```dart
// Example: Coffee powder to decoction
// 100g coffee powder produces 1L decoction
double conversionRatio = 1.0 / 100.0; // 0.01

// Usage in conversion
double rawAmount = 500.0; // 500g
double convertedAmount = rawAmount * conversionRatio; // 5L
```

### Unit Compatibility

The system supports various unit combinations:
- Weight to Volume: gram → liter, kg → liter
- Volume to Volume: ml → liter, liter → ml
- Count to Count: piece → pack, pack → piece
- Custom ratios for specialized conversions

## 📞 Troubleshooting

### Common Issues

**Conversion button not visible**
- Check that item is marked as convertible
- Verify raw stock is greater than zero
- Ensure conversion setup is complete

**Conversion fails with "Invalid ratio"**
- Check conversion ratio is a positive number
- Verify ratio makes sense for the conversion
- Test with a simple ratio like 1.0 first

**Stock quantities don't update**
- Check database permissions
- Verify network connectivity if using remote database
- Review conversion logs for detailed error information

### Support
For technical support with inventory conversions:
1. Check the conversion logs in the app
2. Verify all conversion setup fields are filled
3. Test with small conversion amounts
4. Contact development team with specific error details

---

**Note**: This enhanced inventory conversion system provides professional-grade stock management capabilities suitable for production use in coffee shop and restaurant POS systems.
