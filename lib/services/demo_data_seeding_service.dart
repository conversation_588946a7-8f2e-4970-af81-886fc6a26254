import 'dart:developer';
import 'package:coffee_cofe/database/app_database.dart';
import 'package:coffee_cofe/database/entities/product.dart';
import 'package:coffee_cofe/database/entities/product_category_dto.dart';
import 'package:coffee_cofe/database/entities/inventory_category_dto.dart';
import 'package:coffee_cofe/database/entities/raw_metrials_dto.dart';
import 'package:coffee_cofe/database/entities/sales_dto.dart';
import 'package:coffee_cofe/database/entities/sales_transaction_dto.dart';
import 'package:coffee_cofe/database/entities/workspace_dto.dart';
import 'package:coffee_cofe/models/discounts.dart';

/// Production-level demo data seeding service
/// This service populates the database with realistic business data for demo users
class DemoDataSeedingService {
  static final DemoDataSeedingService _instance = DemoDataSeedingService._internal();
  factory DemoDataSeedingService() => _instance;
  DemoDataSeedingService._internal();

  final AppDatabase _appDatabase = AppDatabase();
  bool _isSeeded = false;

  /// Check if demo data has been seeded
  bool get isSeeded => _isSeeded;

  /// Seed all production-level demo data
  Future<void> seedAllDemoData(String workspaceId, String userId) async {
    if (_isSeeded) {
      log('Demo data already seeded, skipping...');
      return;
    }

    try {
      log('Starting production-level demo data seeding...');

      // Seed in proper order to maintain referential integrity
      await _seedProductCategories(workspaceId);
      await _seedInventoryItems(workspaceId);
      await _seedRawMaterials(workspaceId);
      await _seedProducts(workspaceId);
      await _seedDiscounts(workspaceId);
      await _seedSalesHistory(workspaceId, userId);
      await _seedWorkspaceSettings(workspaceId, userId);
      await _seedGSTConfiguration(workspaceId);

      _isSeeded = true;
      log('Production-level demo data seeding completed successfully');
    } catch (e) {
      log('Error seeding demo data: $e');
      rethrow;
    }
  }

  /// Seed product categories with realistic coffee shop categories
  Future<void> _seedProductCategories(String workspaceId) async {
    final categoryDao = await _appDatabase.productCategoryDao;

    final categories = [
      ProductCategoryDto(
        shopId: workspaceId,
        categoryId: 'CAT_001',
        productCategoryName: 'Hot Beverages',
        createdDate: DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
      ),
      ProductCategoryDto(
        shopId: workspaceId,
        categoryId: 'CAT_002',
        productCategoryName: 'Cold Beverages',
        createdDate: DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
      ),
      ProductCategoryDto(
        shopId: workspaceId,
        categoryId: 'CAT_003',
        productCategoryName: 'Bakery Items',
        createdDate: DateTime.now().subtract(const Duration(days: 25)).toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
      ),
      ProductCategoryDto(
        shopId: workspaceId,
        categoryId: 'CAT_004',
        productCategoryName: 'Snacks',
        createdDate: DateTime.now().subtract(const Duration(days: 20)).toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
      ),
      ProductCategoryDto(
        shopId: workspaceId,
        categoryId: 'CAT_005',
        productCategoryName: 'Specialty Drinks',
        createdDate: DateTime.now().subtract(const Duration(days: 15)).toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
      ),
    ];

    for (final category in categories) {
      await categoryDao.insertProductCategory(category);
    }

    log('Seeded ${categories.length} product categories');
  }

  /// Seed inventory items with realistic coffee shop inventory
  Future<void> _seedInventoryItems(String workspaceId) async {
    final inventoryDao = await _appDatabase.inventoryDao;

    final inventoryItems = [
      // Raw materials
      InventoryDto(
        shopId: workspaceId,
        inventoryId: 'INV_001',
        inventoyProductName: 'Coffee Beans - Arabica',
        inventoryQunatity: '50',
        inventoryUnit: 'kg',
        createdDate: DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
        isConvertible: true,
        outputItemName: 'Ground Coffee',
        outputQuantity: '45',
        outputUnit: 'kg',
        conversionRatio: '0.9',
        convertedStock: '15',
      ),
      InventoryDto(
        shopId: workspaceId,
        inventoryId: 'INV_002',
        inventoyProductName: 'Coffee Beans - Robusta',
        inventoryQunatity: '30',
        inventoryUnit: 'kg',
        createdDate: DateTime.now().subtract(const Duration(days: 28)).toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
        isConvertible: true,
        outputItemName: 'Ground Coffee',
        outputQuantity: '27',
        outputUnit: 'kg',
        conversionRatio: '0.9',
        convertedStock: '10',
      ),
      InventoryDto(
        shopId: workspaceId,
        inventoryId: 'INV_003',
        inventoyProductName: 'Milk',
        inventoryQunatity: '200',
        inventoryUnit: 'liters',
        createdDate: DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
      ),
      InventoryDto(
        shopId: workspaceId,
        inventoryId: 'INV_004',
        inventoyProductName: 'Sugar',
        inventoryQunatity: '25',
        inventoryUnit: 'kg',
        createdDate: DateTime.now().subtract(const Duration(days: 15)).toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
      ),
      InventoryDto(
        shopId: workspaceId,
        inventoryId: 'INV_005',
        inventoyProductName: 'Paper Cups - Small',
        inventoryQunatity: '500',
        inventoryUnit: 'pieces',
        createdDate: DateTime.now().subtract(const Duration(days: 10)).toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
      ),
    ];

    for (final item in inventoryItems) {
      await inventoryDao.insertInventory(item);
    }

    log('Seeded ${inventoryItems.length} inventory items');
  }

  /// Seed raw materials for recipes
  Future<void> _seedRawMaterials(String workspaceId) async {
    final rawMaterialDao = await _appDatabase.rawMaterialDao;

    final rawMaterials = [
      RawMaterialDto(
        rawMaterialId: 'RM_001',
        productId: 'PROD_001', // Espresso
        inventoryId: 'INV_001',
        name: 'Ground Coffee',
        unit: 'grams',
        unitPrice: 2.5,
        quantity: 18.0,
        status: 1,
        rowStates: 1,
      ),
      RawMaterialDto(
        rawMaterialId: 'RM_002',
        productId: 'PROD_002', // Cappuccino
        inventoryId: 'INV_001',
        name: 'Ground Coffee',
        unit: 'grams',
        unitPrice: 2.5,
        quantity: 18.0,
        status: 1,
        rowStates: 1,
      ),
      RawMaterialDto(
        rawMaterialId: 'RM_003',
        productId: 'PROD_002', // Cappuccino
        inventoryId: 'INV_003',
        name: 'Milk',
        unit: 'ml',
        unitPrice: 0.05,
        quantity: 150.0,
        status: 1,
        rowStates: 1,
      ),
      RawMaterialDto(
        rawMaterialId: 'RM_004',
        productId: 'PROD_003', // Latte
        inventoryId: 'INV_001',
        name: 'Ground Coffee',
        unit: 'grams',
        unitPrice: 2.5,
        quantity: 18.0,
        status: 1,
        rowStates: 1,
      ),
      RawMaterialDto(
        rawMaterialId: 'RM_005',
        productId: 'PROD_003', // Latte
        inventoryId: 'INV_003',
        name: 'Milk',
        unit: 'ml',
        unitPrice: 0.05,
        quantity: 200.0,
        status: 1,
        rowStates: 1,
      ),
    ];

    for (final rawMaterial in rawMaterials) {
      await rawMaterialDao.insert(rawMaterial);
    }

    log('Seeded ${rawMaterials.length} raw materials');
  }

  /// Seed products with realistic coffee shop menu
  Future<void> _seedProducts(String workspaceId) async {
    final productDao = await _appDatabase.productDao;

    final products = [
      ProductDto(
        shopId: workspaceId,
        productId: 'PROD_001',
        productUniqId: 'ESP001',
        productName: 'Espresso',
        price: '120',
        mrp: '150',
        categoryId: 'CAT_001',
        cgst: '9',
        sgst: '9',
        costOfProduction: '45',
        createdDate: DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
        recipeRawMaterialId: 'RM_001',
      ),
      ProductDto(
        shopId: workspaceId,
        productId: 'PROD_002',
        productUniqId: 'CAP001',
        productName: 'Cappuccino',
        price: '180',
        mrp: '200',
        categoryId: 'CAT_001',
        cgst: '9',
        sgst: '9',
        costOfProduction: '65',
        createdDate: DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
        recipeRawMaterialId: 'RM_002,RM_003',
      ),
      ProductDto(
        shopId: workspaceId,
        productId: 'PROD_003',
        productUniqId: 'LAT001',
        productName: 'Latte',
        price: '200',
        mrp: '220',
        categoryId: 'CAT_001',
        cgst: '9',
        sgst: '9',
        costOfProduction: '70',
        createdDate: DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
        recipeRawMaterialId: 'RM_004,RM_005',
      ),
      ProductDto(
        shopId: workspaceId,
        productId: 'PROD_004',
        productUniqId: 'ICE001',
        productName: 'Iced Coffee',
        price: '160',
        mrp: '180',
        categoryId: 'CAT_002',
        cgst: '9',
        sgst: '9',
        costOfProduction: '55',
        createdDate: DateTime.now().subtract(const Duration(days: 25)).toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
      ),
      ProductDto(
        shopId: workspaceId,
        productId: 'PROD_005',
        productUniqId: 'CRO001',
        productName: 'Croissant',
        price: '80',
        mrp: '100',
        categoryId: 'CAT_003',
        cgst: '9',
        sgst: '9',
        costOfProduction: '35',
        createdDate: DateTime.now().subtract(const Duration(days: 20)).toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
      ),
    ];

    for (final product in products) {
      await productDao.insertProduct(product);
    }

    log('Seeded ${products.length} products');
  }

  /// Seed discount configurations
  Future<void> _seedDiscounts(String workspaceId) async {
    final discountDao = await _appDatabase.enhancedDiscountDao;

    final discounts = [
      Discounts(
        discountID: 'DISC_001',
        couponName: 'Welcome Offer',
        discountType: 'percentage',
        formula: '10',
        mode: 'manual',
        categoryID: 'ALL',
        minDiscount: '100',
        maxDiscount: '50',
        createdDate: DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
        createdBy: 'ADMIN',
        discountActive: 1,
        discountSync: 1,
        workspaceId: workspaceId,
        isActive: '1',
      ),
      Discounts(
        discountID: 'DISC_002',
        couponName: 'Happy Hour',
        discountType: 'percentage',
        formula: '20',
        mode: 'automatic',
        categoryID: 'CAT_001',
        minDiscount: '200',
        maxDiscount: '100',
        createdDate: DateTime.now().subtract(const Duration(days: 15)).toIso8601String(),
        createdBy: 'ADMIN',
        discountActive: 1,
        discountSync: 1,
        workspaceId: workspaceId,
        isActive: '1',
      ),
    ];

    for (final discount in discounts) {
      await discountDao.insertDiscount(discount);
    }

    log('Seeded ${discounts.length} discounts');
  }

  /// Seed sales history with realistic transactions
  Future<void> _seedSalesHistory(String workspaceId, String userId) async {
    final salesDao = await _appDatabase.salesDao;
    final salesTransactionDao = await _appDatabase.salesTransactionDao;

    // Generate sales for the last 30 days
    final now = DateTime.now();
    final salesData = <Map<String, dynamic>>[];

    for (int i = 0; i < 30; i++) {
      final saleDate = now.subtract(Duration(days: i));
      final dailySales = _generateDailySales(saleDate, workspaceId, userId);
      salesData.addAll(dailySales);
    }

    // Insert sales and transactions
    for (final saleData in salesData) {
      final sale = saleData['sale'] as SalesDto;
      final transactions = saleData['transactions'] as List<SalesTransactionDto>;

      await salesDao.insertSale(sale);

      for (final transaction in transactions) {
        await salesTransactionDao.insertTransaction(transaction);
      }
    }

    log('Seeded ${salesData.length} sales records with transactions');
  }

  /// Generate realistic daily sales data
  List<Map<String, dynamic>> _generateDailySales(DateTime date, String workspaceId, String userId) {
    final salesList = <Map<String, dynamic>>[];
    final random = DateTime.now().millisecondsSinceEpoch % 100;
    final dailySalesCount = 5 + (random % 15); // 5-20 sales per day

    for (int i = 0; i < dailySalesCount; i++) {
      final saleId = 'SALE_${date.millisecondsSinceEpoch}_$i';
      final transactionId = 'TXN_${date.millisecondsSinceEpoch}_$i';

      // Random products for this sale
      final productIds = ['PROD_001', 'PROD_002', 'PROD_003', 'PROD_004', 'PROD_005'];
      final selectedProducts = <String>[];
      final itemCount = 1 + (random % 3); // 1-3 items per sale

      for (int j = 0; j < itemCount; j++) {
        selectedProducts.add(productIds[random % productIds.length]);
      }

      double totalAmount = 0;
      final transactions = <SalesTransactionDto>[];

      for (int k = 0; k < selectedProducts.length; k++) {
        final productId = selectedProducts[k];
        final quantity = 1 + (random % 2); // 1-2 quantity
        final price = _getProductPrice(productId);
        final amount = price * quantity;
        totalAmount += amount;

        transactions.add(SalesTransactionDto(
          salesTransactionId: '${transactionId}_$k',
          salesId: saleId,
          productId: productId,
          productQty: quantity,
          productAmount: amount,
          discountAmount: 0.0,
          createdAt: date.toIso8601String(),
          status: 1,
          rowStatus: 1,
        ));
      }

      final sale = SalesDto(
        salesId: saleId,
        salesTransactionId: transactionId,
        totalAmount: totalAmount,
        customerId: 'CUST_${random % 100}',
        createdDate: date.toIso8601String(),
        updatedDate: date.toIso8601String(),
        status: 1,
        rowStatus: 1,
      );

      salesList.add({
        'sale': sale,
        'transactions': transactions,
      });
    }

    return salesList;
  }

  /// Get product price for sales calculation
  double _getProductPrice(String productId) {
    final prices = {
      'PROD_001': 120.0, // Espresso
      'PROD_002': 180.0, // Cappuccino
      'PROD_003': 200.0, // Latte
      'PROD_004': 160.0, // Iced Coffee
      'PROD_005': 80.0, // Croissant
    };
    return prices[productId] ?? 100.0;
  }

  /// Seed workspace settings
  Future<void> _seedWorkspaceSettings(String workspaceId, String userId) async {
    final workspaceDao = await _appDatabase.workspaceSettingsDao;

    final settings = [
      WorkspaceSettingsDto(
        workSpaceId: workspaceId,
        wSName: 'Coffee Cofe Demo Shop',
        wSUserName: 'Demo Admin',
        wSUserId: userId,
        wSSettingKey: 'business_name',
        wSSettingValue: 'Coffee Cofe Demo Shop',
        workSsettingSync: 1,
      ),
      WorkspaceSettingsDto(
        workSpaceId: workspaceId,
        wSName: 'Coffee Cofe Demo Shop',
        wSUserName: 'Demo Admin',
        wSUserId: userId,
        wSSettingKey: 'business_address',
        wSSettingValue: '123 Demo Street, Coffee City, CC 12345',
        workSsettingSync: 1,
      ),
      WorkspaceSettingsDto(
        workSpaceId: workspaceId,
        wSName: 'Coffee Cofe Demo Shop',
        wSUserName: 'Demo Admin',
        wSUserId: userId,
        wSSettingKey: 'business_phone',
        wSSettingValue: '******-COFFEE',
        workSsettingSync: 1,
      ),
      WorkspaceSettingsDto(
        workSpaceId: workspaceId,
        wSName: 'Coffee Cofe Demo Shop',
        wSUserName: 'Demo Admin',
        wSUserId: userId,
        wSSettingKey: 'tax_enabled',
        wSSettingValue: 'true',
        workSsettingSync: 1,
      ),
      WorkspaceSettingsDto(
        workSpaceId: workspaceId,
        wSName: 'Coffee Cofe Demo Shop',
        wSUserName: 'Demo Admin',
        wSUserId: userId,
        wSSettingKey: 'default_gst_rate',
        wSSettingValue: '18.0',
        workSsettingSync: 1,
      ),
    ];

    for (final setting in settings) {
      await workspaceDao.insert(setting);
    }

    log('Seeded ${settings.length} workspace settings');
  }

  /// Seed GST configuration
  Future<void> _seedGSTConfiguration(String workspaceId) async {
    try {
      // Insert default GST configuration using raw SQL since we don't have a specific DAO
      final db = await _appDatabase.database;

      // Insert simple GST config
      await db.insert('SimpleGSTConfig', {
        'businessName': 'Coffee Cofe Demo Shop',
        'gstin': 'DEMO123456789',
        'defaultGSTRate': 18.0,
        'enableGST': 1,
        'createdDate': DateTime.now().toIso8601String(),
        'updatedDate': DateTime.now().toIso8601String(),
      });

      log('Seeded GST configuration');
    } catch (e) {
      log('Error seeding GST configuration: $e');
      // Continue without failing the entire seeding process
    }
  }

  /// Reset seeding status (for testing purposes)
  void resetSeedingStatus() {
    _isSeeded = false;
  }

  /// Check if specific data exists to avoid duplicate seeding
  Future<bool> _hasExistingData() async {
    try {
      final productDao = await _appDatabase.productDao;
      final products = await productDao.getAllProducts();
      return products.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}
