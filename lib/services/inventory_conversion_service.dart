import 'dart:developer';
import '../database/entities/inventory_category_dto.dart';

/// Service for handling inventory conversion operations with enhanced validation and error handling
class InventoryConversionService {
  /// Validate conversion parameters before performing conversion
  static ConversionValidationResult validateConversion({
    required InventoryDto inventory,
    required double amountToConvert,
  }) {
    // Check if inventory is convertible
    if (!inventory.canConvert) {
      return ConversionValidationResult(
        isValid: false,
        errorMessage: 'This inventory item is not convertible',
        errorCode: ConversionErrorCode.notConvertible,
      );
    }

    // Check if amount is positive
    if (amountToConvert <= 0) {
      return ConversionValidationResult(
        isValid: false,
        errorMessage: 'Conversion amount must be greater than zero',
        errorCode: ConversionErrorCode.invalidAmount,
      );
    }

    // Check if there's enough raw stock
    if (amountToConvert > inventory.rawQuantity) {
      return ConversionValidationResult(
        isValid: false,
        errorMessage:
            'Insufficient raw stock. Available: ${inventory.rawQuantity.toStringAsFixed(2)} ${inventory.inventoryUnit}',
        errorCode: ConversionErrorCode.insufficientStock,
      );
    }

    // Check conversion ratio validity
    if (inventory.conversionRatioValue <= 0) {
      return ConversionValidationResult(
        isValid: false,
        errorMessage: 'Invalid conversion ratio: ${inventory.conversionRatio}',
        errorCode: ConversionErrorCode.invalidRatio,
      );
    }

    // All validations passed
    return ConversionValidationResult(
      isValid: true,
      errorMessage: null,
      errorCode: null,
    );
  }

  /// Calculate conversion result with detailed information
  static ConversionCalculationResult calculateConversion({
    required InventoryDto inventory,
    required double amountToConvert,
  }) {
    final validation = validateConversion(
      inventory: inventory,
      amountToConvert: amountToConvert,
    );

    if (!validation.isValid) {
      return ConversionCalculationResult(
        isValid: false,
        errorMessage: validation.errorMessage!,
        errorCode: validation.errorCode!,
        rawAmountToConvert: 0,
        convertedAmountProduced: 0,
        newRawQuantity: inventory.rawQuantity,
        newConvertedQuantity: inventory.convertedQuantity,
      );
    }

    final convertedAmountProduced = amountToConvert * inventory.conversionRatioValue;
    final newRawQuantity = inventory.rawQuantity - amountToConvert;
    final newConvertedQuantity = inventory.convertedQuantity + convertedAmountProduced;

    return ConversionCalculationResult(
      isValid: true,
      errorMessage: null,
      errorCode: null,
      rawAmountToConvert: amountToConvert,
      convertedAmountProduced: convertedAmountProduced,
      newRawQuantity: newRawQuantity,
      newConvertedQuantity: newConvertedQuantity,
    );
  }

  /// Create updated inventory DTO after conversion
  static InventoryDto createUpdatedInventory({
    required InventoryDto originalInventory,
    required ConversionCalculationResult calculationResult,
  }) {
    if (!calculationResult.isValid) {
      throw ArgumentError('Cannot create updated inventory from invalid calculation result');
    }

    return InventoryDto(
      id: originalInventory.id,
      shopId: originalInventory.shopId,
      inventoryId: originalInventory.inventoryId,
      inventoyProductName: originalInventory.inventoyProductName,
      inventoryQunatity: calculationResult.newRawQuantity.toString(),
      inventoryUnit: originalInventory.inventoryUnit,
      createdDate: originalInventory.createdDate,
      updatedDate: DateTime.now().toIso8601String(),
      status: originalInventory.status,
      rowStatus: originalInventory.rowStatus,
      isConvertible: originalInventory.isConvertible,
      outputItemName: originalInventory.outputItemName,
      outputQuantity: originalInventory.outputQuantity,
      outputUnit: originalInventory.outputUnit,
      conversionRatio: originalInventory.conversionRatio,
      convertedStock: calculationResult.newConvertedQuantity.toString(),
    );
  }

  /// Get conversion summary for display
  static String getConversionSummary({
    required InventoryDto inventory,
    required double amountToConvert,
  }) {
    final calculation = calculateConversion(
      inventory: inventory,
      amountToConvert: amountToConvert,
    );

    if (!calculation.isValid) {
      return 'Conversion failed: ${calculation.errorMessage}';
    }

    return 'Converting ${amountToConvert.toStringAsFixed(2)} ${inventory.inventoryUnit} '
        'of ${inventory.inventoyProductName} to '
        '${calculation.convertedAmountProduced.toStringAsFixed(2)} ${inventory.outputUnit} '
        'of ${inventory.outputItemName}';
  }

  /// Validate conversion setup fields
  static ConversionSetupValidationResult validateConversionSetup({
    required String outputItemName,
    required String outputQuantity,
    required String? outputUnit,
    required String conversionRatio,
  }) {
    final errors = <String>[];

    // Validate output item name
    if (outputItemName.trim().isEmpty) {
      errors.add('Output item name is required');
    }

    // Validate output quantity
    if (outputQuantity.trim().isEmpty) {
      errors.add('Output quantity is required');
    } else {
      final qty = double.tryParse(outputQuantity.trim());
      if (qty == null || qty <= 0) {
        errors.add('Output quantity must be a positive number');
      }
    }

    // Validate output unit
    if (outputUnit == null || outputUnit.trim().isEmpty) {
      errors.add('Output unit is required');
    }

    // Validate conversion ratio
    if (conversionRatio.trim().isEmpty) {
      errors.add('Conversion ratio is required');
    } else {
      final ratio = double.tryParse(conversionRatio.trim());
      if (ratio == null || ratio <= 0) {
        errors.add('Conversion ratio must be a positive number');
      }
    }

    return ConversionSetupValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// Log conversion operation for audit trail
  static void logConversion({
    required InventoryDto inventory,
    required double amountConverted,
    required double amountProduced,
    required bool success,
    String? errorMessage,
  }) {
    final logMessage = success
        ? 'CONVERSION SUCCESS: ${inventory.inventoyProductName} - '
            'Converted ${amountConverted.toStringAsFixed(2)} ${inventory.inventoryUnit} '
            'to ${amountProduced.toStringAsFixed(2)} ${inventory.outputUnit}'
        : 'CONVERSION FAILED: ${inventory.inventoyProductName} - '
            'Error: ${errorMessage ?? "Unknown error"}';

    log(logMessage, name: 'InventoryConversion');
  }
}

/// Result of conversion validation
class ConversionValidationResult {
  final bool isValid;
  final String? errorMessage;
  final ConversionErrorCode? errorCode;

  ConversionValidationResult({
    required this.isValid,
    this.errorMessage,
    this.errorCode,
  });
}

/// Result of conversion calculation
class ConversionCalculationResult {
  final bool isValid;
  final String? errorMessage;
  final ConversionErrorCode? errorCode;
  final double rawAmountToConvert;
  final double convertedAmountProduced;
  final double newRawQuantity;
  final double newConvertedQuantity;

  ConversionCalculationResult({
    required this.isValid,
    this.errorMessage,
    this.errorCode,
    required this.rawAmountToConvert,
    required this.convertedAmountProduced,
    required this.newRawQuantity,
    required this.newConvertedQuantity,
  });
}

/// Result of conversion setup validation
class ConversionSetupValidationResult {
  final bool isValid;
  final List<String> errors;

  ConversionSetupValidationResult({
    required this.isValid,
    required this.errors,
  });
}

/// Result of a conversion operation
class ConversionResult {
  final bool success;
  final String? errorMessage;
  final ConversionErrorCode? errorCode;
  final double? rawAmountConverted;
  final double? convertedAmountProduced;
  final double? newRawQuantity;
  final double? newConvertedQuantity;

  ConversionResult({
    required this.success,
    this.errorMessage,
    this.errorCode,
    this.rawAmountConverted,
    this.convertedAmountProduced,
    this.newRawQuantity,
    this.newConvertedQuantity,
  });
}

/// Error codes for conversion operations
enum ConversionErrorCode {
  notConvertible,
  invalidAmount,
  insufficientStock,
  invalidRatio,
  databaseError,
  unknown,
}
