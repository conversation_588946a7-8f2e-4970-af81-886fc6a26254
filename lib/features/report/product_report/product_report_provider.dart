import 'dart:developer';

import 'package:flutter/material.dart';

import '../../../database/app_database.dart';
import '../../../database/dao/product_dao.dart';
import '../../../database/dao/sales_transation_dao.dart';
import '../../../database/entities/product.dart';
import '../../../database/entities/sales_transaction_dto.dart';
import '../../../widgets/common_dropdown.dart';

class ProductReportProvider with ChangeNotifier {
  ProductDAO? _productDAO;
  List<ProductDto> _products = [];
  List<ProductDto> get products => _products;
  List<DropdownItem> dropDownProducts = [];

  Future<void> initializeDAO() async {
    try {
      _productDAO = await AppDatabase().productDao;
      salesTransactionDao = await AppDatabase().salesTransactionDao;

      await fetchProducts();
    } catch (e) {
      log('Failed to initialize the database: $e');
      notifyListeners();
    }
  }

  Future<void> fetchProducts() async {
    try {
      if (_productDAO != null) {
        final list = await _productDAO!.getAllProducts();
        _products = list;

        dropDownProducts = [
          DropdownItem(id: 'all', label: "ALL"),
          ...products.map((item) => DropdownItem(
                id: item.productId ?? '',
                label: item.productName ?? '',
              )),
        ];

        notifyListeners();
      } else {
        log('Database not initialized');
      }
    } catch (e) {
      log('Failed to fetch products: $e');
    } finally {
      notifyListeners();
    }
  }

  DropdownItem? _selectedProductForFilter;
  DropdownItem? get selectedProductForFilter => _selectedProductForFilter;

  onChageProductFilter(DropdownItem product) async {
    _selectedProductForFilter = product;
    await loadTransactions();
    notifyListeners();
  }

  List<SalesTransactionDto> filteredTransactions = [];
  List<SalesTransactionDto> transactions = [];
  SalesTransactionDao? salesTransactionDao;
  // Load all transactions from the database

  double totalQty = 0;
  double totalAmount = 0;
  List<ProductReportItem> groupedProductReports = [];

  Future<void> loadTransactions() async {
    final result = await salesTransactionDao!.getAllTransactions();
    transactions = result;

    final Map<String, ProductReportItem> grouped = {};

    for (final txn in transactions) {
      // Apply filter
      if (_selectedProductForFilter != null &&
          _selectedProductForFilter!.id != 'all' &&
          txn.productId != _selectedProductForFilter!.id) {
        continue;
      }

      final product = products.firstWhere((p) => p.productId == txn.productId, orElse: () => ProductDto());

      final existing = grouped[txn.productId!];
      final qty = txn.productQty ?? 0;
      final price = txn.productAmount ?? 0;
      final total = qty * price;

      if (existing != null) {
        grouped[txn.productId!] = ProductReportItem(
          productId: txn.productId!,
          productName: product.productName ?? '-',
          totalQty: existing.totalQty + qty,
          unitPrice: price,
          totalAmount: existing.totalAmount + total,
        );
      } else {
        grouped[txn.productId!] = ProductReportItem(
          productId: txn.productId!,
          productName: product.productName ?? '-',
          totalQty: qty,
          unitPrice: price,
          totalAmount: total,
        );
      }
    }

    groupedProductReports = grouped.values.toList();

    totalQty = groupedProductReports.fold(0, (sum, e) => sum + e.totalQty);
    totalAmount = groupedProductReports.fold(0, (sum, e) => sum + e.totalAmount);

    notifyListeners();
  }
}

class ProductReportItem {
  final String productId;
  final String productName;
  final int totalQty;
  final double unitPrice;
  final double totalAmount;

  ProductReportItem({
    required this.productId,
    required this.productName,
    required this.totalQty,
    required this.unitPrice,
    required this.totalAmount,
  });
}
