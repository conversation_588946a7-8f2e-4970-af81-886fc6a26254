import 'package:coffee_cofe/core/styles.dart';
import 'package:coffee_cofe/features/inventory/inventory_screen_provider.dart';
import 'package:coffee_cofe/features/profile/profile_settings_controller.dart';
import 'package:coffee_cofe/widgets/common_appbar.dart';
import 'package:coffee_cofe/widgets/common_drawer.dart';
import 'package:coffee_cofe/widgets/input_form_field.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../database/entities/inventory_category_dto.dart';
import '../../widgets/common_dropdown.dart';
import '../../widgets/label_with_widget.dart';

class InventoryScreen extends StatefulWidget {
  const InventoryScreen({super.key});

  @override
  State<InventoryScreen> createState() => _InventoryScreenState();
}

class _InventoryScreenState extends State<InventoryScreen> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 500), () {
      initFunction();
    });
  }

  initFunction() async {
    await Provider.of<InventoryScreenProvider>(context, listen: false).initFunction();
    setState(() {}); // Ensure the UI is refreshed after initial data loading
  }

  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = ''; // Store the search query

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<InventoryScreenProvider>(context);

    return ValueListenableBuilder<Color>(
        valueListenable: ProfileController.themeColorNotifier,
        builder: (context, bgColor, child) {
          return Scaffold(
              drawer: const CommonDrawer(),
              floatingActionButton: Padding(
                padding: const EdgeInsets.only(left: 10.0, bottom: 25, right: 25),
                child: FloatingActionButton(
                  backgroundColor: bgColor,
                  onPressed: () {
                    addInventoryProduct(provider);
                  },
                  child: const Icon(
                    Icons.add,
                    size: 35,
                    color: Colors.white,
                  ),
                ),
              ),
              appBar: CommonAppBar(
                title: 'Inventory',
                appBarColor: bgColor,
              ),
              body: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    InputFormField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Search Inventory...',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value.toLowerCase(); // Update the search query
                        });
                      },
                    ),
                    Flexible(
                      child: ListView.builder(
                        itemCount: provider.filteredInventoryList(_searchQuery).length,
                        itemBuilder: (context, index) {
                          final item = provider.filteredInventoryList(_searchQuery)[index];

                          // Parse quantity to double safely
                          double qty = double.tryParse(item.inventoryQunatity ?? "0") ?? 0;

                          bool isLowStock = false;
                          if (item.inventoryUnit == "Kg" || item.inventoryUnit == "Litres") {
                            isLowStock = qty < 1;
                          } else if (item.inventoryUnit == "No" || item.inventoryUnit == "Pack") {
                            isLowStock = qty < 5;
                          }

                          return Container(
                            margin: const EdgeInsets.symmetric(vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.2),
                                  blurRadius: 6,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                            ),
                            child: Stack(
                              children: [
                                ListTile(
                                  leading: CircleAvatar(
                                    backgroundColor: isLowStock ? Colors.redAccent : Colors.blueAccent,
                                    child: Text(
                                      item.inventoyProductName?.substring(0, 1).toUpperCase() ?? '?',
                                      style: const TextStyle(color: Colors.white),
                                    ),
                                  ),
                                  title: Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          item.inventoyProductName ?? "",
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                      ),
                                      if (item.isConvertible == true)
                                        Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                          decoration: BoxDecoration(
                                            color: Colors.green.shade100,
                                            borderRadius: BorderRadius.circular(4),
                                            border: Border.all(color: Colors.green.shade300),
                                          ),
                                          child: Text(
                                            'Convertible',
                                            style: TextStyle(
                                              color: Colors.green.shade700,
                                              fontSize: 10,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  subtitle: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Raw: ${double.tryParse(item.inventoryQunatity ?? "0")?.toStringAsFixed(2)} ${item.inventoryUnit ?? ""}',
                                        style: const TextStyle(fontSize: 14),
                                      ),
                                      if (item.isConvertible == true && item.convertedQuantity > 0)
                                        Text(
                                          'Converted: ${item.convertedQuantity.toStringAsFixed(2)} ${item.outputUnit ?? ""}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.green.shade600,
                                            fontStyle: FontStyle.italic,
                                          ),
                                        ),
                                      if (item.isConvertible == true && item.canConvert)
                                        Text(
                                          'Can convert to: ${item.maxConvertibleAmount.toStringAsFixed(2)} ${item.outputUnit ?? ""}',
                                          style: TextStyle(
                                            fontSize: 11,
                                            color: Colors.blue.shade600,
                                          ),
                                        ),
                                    ],
                                  ),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      if (item.canConvert)
                                        IconButton(
                                          icon: Icon(Icons.transform, color: Colors.green.shade600),
                                          tooltip: 'Convert to ${item.outputItemName}',
                                          onPressed: () => _showConversionDialog(item, provider),
                                        ),
                                      IconButton(
                                        icon: const Icon(Icons.edit, color: Colors.grey),
                                        onPressed: () async {
                                          await provider.oneEdit(item); // ✅ Mark it as editing

                                          await addInventoryProduct(provider); // Open your bottomsheet
                                          await provider.loadInventories(); // Reload the latest data
                                          setState(() {}); // Refresh the UI after the data has been loaded
                                        },
                                      ),
                                    ],
                                  ),
                                ),

                                // Low stock indicator if stock is low
                                if (isLowStock)
                                  Positioned(
                                    right: 75,
                                    top: 25,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: Colors.redAccent,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: const Text(
                                        'Low Stock',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 10,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ));
        });
  }

  void _showConversionDialog(InventoryDto item, InventoryScreenProvider provider) {
    final TextEditingController amountController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Convert ${item.inventoyProductName}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Available: ${item.rawQuantity.toStringAsFixed(2)} ${item.inventoryUnit}'),
              Text('Converts to: ${item.outputItemName}'),
              Text('Ratio: 1 ${item.inventoryUnit} → ${item.conversionRatioValue} ${item.outputUnit}'),
              const SizedBox(height: 16),
              TextField(
                controller: amountController,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                decoration: InputDecoration(
                  labelText: 'Amount to convert (${item.inventoryUnit})',
                  border: const OutlineInputBorder(),
                  hintText: 'Enter amount',
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                final amount = double.tryParse(amountController.text);
                if (amount != null && amount > 0 && amount <= item.rawQuantity) {
                  final navigator = Navigator.of(context);
                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  navigator.pop();
                  final result = await provider.convertInventory(item, amount);
                  if (result.success && mounted) {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text(
                            'Successfully converted ${result.rawAmountConverted?.toStringAsFixed(2)} ${item.inventoryUnit} to ${result.convertedAmountProduced?.toStringAsFixed(2)} ${item.outputUnit}'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  } else if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text(result.errorMessage ?? 'Failed to convert inventory'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please enter a valid amount'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                }
              },
              child: const Text('Convert'),
            ),
          ],
        );
      },
    );
  }

  addInventoryProduct(InventoryScreenProvider provider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Allow custom height
      showDragHandle: true,
      useSafeArea: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (BuildContext context, setState) {
            return Padding(
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                top: 20, // Adjust based on keyboard height
              ),
              child: SizedBox(
                height: MediaQuery.of(context).size.height - 50, // Full screen height
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      // Title Section
                      Align(
                        alignment: Alignment.topLeft,
                        child: Text(
                          provider.selectedInventory == null ? 'Add Inventory' : 'Update Inventory',
                          style: black22w500,
                          textAlign: TextAlign.left,
                        ),
                      ),
                      const SizedBox(height: 20), // Space between title and form

                      // Form fields
                      LabeledFormField(
                        "Product Name *",
                        InputFormField(
                          autoFocus: true,
                          hint: "Enter product name",
                          controller: provider.nameController,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          Expanded(
                            child: LabeledFormField(
                              "Quantity",
                              InputFormField(
                                hint: "Enter quantity",
                                controller: provider.quantityController,
                                inputType: const TextInputType.numberWithOptions(decimal: true),
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: SizedBox(
                              width: 140,
                              child: LabeledFormField(
                                "Unit",
                                CommonDropdown(
                                  enableMargine: false,
                                  items: provider.units,
                                  hitLabel: "Select Unit",
                                  selectedItem: provider.selectedUnit,
                                  onChanged: (value) async {
                                    if (value != null) {
                                      provider.onChageunit(value);
                                    }
                                    setState(() {}); // Trigger a UI update
                                  },
                                  isSearchable: false,
                                  // itemLabel: (DropdownItem item) => item.label,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 15),

                      // Convertible Inventory Section
                      CheckboxListTile(
                        title: const Text(
                          'Convertible',
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        subtitle: const Text('Enable conversion to another product'),
                        value: provider.isConvertible,
                        onChanged: (value) {
                          provider.setConvertible(value ?? false);
                          setState(() {});
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                        contentPadding: EdgeInsets.zero,
                      ),

                      // Conversion fields (shown only when convertible is checked)
                      if (provider.isConvertible) ...[
                        const SizedBox(height: 10),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.blue.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Conversion Settings',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.blue,
                                ),
                              ),
                              const SizedBox(height: 10),
                              LabeledFormField(
                                "Output Item Name *",
                                InputFormField(
                                  hint: "e.g., Coffee Decoction",
                                  controller: provider.outputItemNameController,
                                ),
                              ),
                              const SizedBox(height: 10),
                              Row(
                                children: [
                                  Expanded(
                                    child: LabeledFormField(
                                      "Output Quantity *",
                                      InputFormField(
                                        hint: "e.g., 10",
                                        controller: provider.outputQuantityController,
                                        inputType: const TextInputType.numberWithOptions(decimal: true),
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 8.0),
                                    child: SizedBox(
                                      width: 140,
                                      child: LabeledFormField(
                                        "Output Unit *",
                                        CommonDropdown(
                                          enableMargine: false,
                                          items: provider.units,
                                          hitLabel: "Select Unit",
                                          selectedItem: provider.selectedOutputUnit,
                                          onChanged: (value) async {
                                            if (value != null) {
                                              provider.onChangeOutputUnit(value);
                                            }
                                            setState(() {});
                                          },
                                          isSearchable: false,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 10),
                              LabeledFormField(
                                "Conversion Formula",
                                InputFormField(
                                  hint: "e.g., 100g → 1L (ratio: 0.01)",
                                  controller: provider.conversionRatioController,
                                  inputType: const TextInputType.numberWithOptions(decimal: true),
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Formula: ${provider.quantityController.text.isEmpty ? "X" : provider.quantityController.text} ${provider.selectedUnit?.label ?? "unit"} → ${provider.outputQuantityController.text.isEmpty ? "Y" : provider.outputQuantityController.text} ${provider.selectedOutputUnit?.label ?? "unit"}',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],

                      const SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: () async {
                          if (provider.nameController.text.isNotEmpty) {
                            final navigator = Navigator.of(context);
                            final scaffoldMessenger = ScaffoldMessenger.of(context);
                            try {
                              final success = await provider.saveCategory();
                              if (success && mounted) {
                                navigator.pop();

                                scaffoldMessenger.showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      provider.selectedInventory == null
                                          ? 'Inventory added successfully!'
                                          : 'Inventory updated successfully!',
                                    ),
                                  ),
                                );

                                // Refresh the inventory list immediately after adding or updating
                                await provider.loadInventories();
                                setState(() {}); // Trigger UI update to reflect the latest data
                              } else if (!success && mounted) {
                                scaffoldMessenger.showSnackBar(
                                  const SnackBar(
                                    content: Text('Failed to save inventory. Please try again!'),
                                  ),
                                );
                              }
                            } catch (e) {
                              if (mounted) {
                                scaffoldMessenger.showSnackBar(
                                  SnackBar(
                                    content: Text('Error occurred: ${e.toString()}'),
                                  ),
                                );
                              }
                            } finally {
                              provider.clearCategoryForm(); // Clear form after the operation
                            }
                          }
                        },
                        child:
                            Text(provider.selectedInventory == null ? 'Add Inventory' : 'Update Inventory'),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    ).whenComplete(() async {
      await provider.clearCategoryForm();
    });
  }
}
