import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:coffee_cofe/main.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import '../../database/app_database.dart';
import '../../database/dao/inventory_dao.dart';
import '../../database/dao/product_category_dao.dart';
import '../../database/dao/product_dao.dart';
import '../../database/dao/raw_metrials_dao.dart';
import '../../database/entities/inventory_category_dto.dart';
import '../../database/entities/product.dart';
import '../../database/entities/product_category_dto.dart';
import '../../database/entities/raw_metrials_dto.dart';
import '../../widgets/common_dropdown.dart';

class ProductProvider extends ChangeNotifier {
  ProductDAO? _productDAO;
  List<ProductDto> _products = [];
  bool _isLoading = false;
  String? _errorMessage;
  List<ProductDto> get products => _products;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  final TextEditingController nameController = TextEditingController();
  final TextEditingController priceController = TextEditingController();
  final TextEditingController gstController = TextEditingController();
  final TextEditingController copController = TextEditingController();
  final TextEditingController discountController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController parcelAmountController = TextEditingController();

  ProductDto? selectedProduct;
  Timer? _debounce;
  File? _imageFile;
  File? get imageFile => _imageFile;

  final ImagePicker _picker = ImagePicker();

  Future _initializeDAO() async {
    try {
      _productDAO = await AppDatabase().productDao;
      _producCategorytDAO = await AppDatabase().productCategoryDao;
      _rawMaterialDao = await AppDatabase().rawMaterialDao;
      _inventoryDAO = await AppDatabase().inventoryDao;
    } catch (e) {
      _errorMessage = 'Failed to initialize the database: $e';
      notifyListeners();
    }
  }

  Future<void> fetchProducts() async {
    _setLoading(true);
    try {
      if (_productDAO != null) {
        _products = await _productDAO!.getAllProducts();
        _errorMessage = null;
      } else {
        _errorMessage = 'Database not initialized';
      }
    } catch (e) {
      log(e.toString());
      _errorMessage = 'Failed to fetch products: $e';
    } finally {
      _setLoading(false);
    }
  }

  assignTextFormFields(int? selectedIndex) async {
    selectedProduct = _products[selectedIndex ?? -1];
    nameController.text = selectedProduct?.productName ?? "";
    priceController.text = selectedProduct?.price ?? "";
    gstController.text = selectedProduct?.cgst ?? "";
    discountController.text = selectedProduct?.productDiscountAmount ?? "";
    parcelAmountController.text = selectedProduct?.parcelAmount ?? "";
    copController.text = selectedProduct?.costOfProduction ?? '';

    int selectedCategory = int.tryParse(selectedProduct?.categoryId ?? "-1") ?? 1;
    selectedProductUId = selectedProduct?.productId ?? "pId";
    selectedProductId = selectedProduct?.id ?? 0;
    _imageFile = File(selectedProduct?.productImagePath ?? '');
    await getAllRawMetrialsByProductId(selectedProduct!.productId!);
    _selectedCategory = _categoriesList[selectedCategory];

    log(_recipeData1.length.toString());
    notifyListeners();
  }

  initFunction() async {
    await resetFields();
    await _initializeDAO();

    await fetchProducts();
    await loadCategories();
    await loadInventories();
    onChageCat(categoriesList.first);
  }

  void feedCostOfProduction() {
    // Cancel any existing timer
    _debounce?.cancel();

    // Start a new debounce timer
    _debounce = Timer(const Duration(milliseconds: 500), () {
      double mrp = double.tryParse(priceController.text.trim()) ?? 0.0;
      double gst = double.tryParse(gstController.text.trim()) ?? 0.0;
      copController.text = (mrp + gst).toStringAsFixed(2);
      notifyListeners();
    });
  }

  void pickImage() async {
    final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile == null) return;

    final appDir = await getApplicationDocumentsDirectory();
    final fileName = pickedFile.name;
    _imageFile = File('${appDir.path}/$fileName');
    await File(pickedFile.path).copy(_imageFile!.path);
    notifyListeners();
  }

  String? productId;
  String? selectedProductUId;
  int? selectedProductId;

  void addOrUpdateProduct({bool addMore = false}) async {
    if (nameController.text.isEmpty || priceController.text.isEmpty) return;

    try {
      var uuid = const Uuid();
      // Calculate CGST and SGST from total GST rate
      final totalGstRate = double.tryParse(gstController.text.trim()) ?? 0;
      final cgstRate = totalGstRate / 2; // Split GST equally between CGST and SGST
      final sgstRate = totalGstRate / 2;

      final product = ProductDto(
        shopId: MyApp.shopId,
        productId: uuid.v4(),
        productName: nameController.text.trim(),
        createdDate: DateTime.now().toIso8601String(),
        cgst: cgstRate.toString(),
        sgst: sgstRate.toString(),
        productDiscountAmount: discountController.text,
        costOfProduction: copController.text.trim(),
        rowStatus: 0,
        price: double.parse(priceController.text).toString(),
        categoryId: selectedCategory?.id,
        parcelAmount: double.tryParse(parcelAmountController.text).toString(),
        productImagePath: _imageFile?.path ?? '',
      );

      // Check if we are updating or adding
      if (selectedProductUId == null) {
        await insertRawMaterials(product.productId ?? '');

        await saveAddProduct(product);
      } else {
        final updatedProduct = ProductDto(
          id: selectedProductId,
          shopId: MyApp.shopId,
          productId: selectedProductUId,
          productName: nameController.text,
          createdDate: DateTime.now().toIso8601String(),
          cgst: gstController.text,
          sgst: gstController.text,
          costOfProduction: copController.text.trim(),
          productDiscountAmount: discountController.text,
          rowStatus: 0,
          price: double.parse(priceController.text).toString(),
          categoryId: selectedCategory?.id,
          parcelAmount: double.tryParse(parcelAmountController.text).toString(),
          productImagePath: _imageFile?.path ?? '',
        );
        // Updating an existing product
        await updateProduct(updatedProduct);
        await insertRawMaterials(updatedProduct.productId ?? '');
        resetFields();
      }

      if (!addMore) {
        Get.back(result: 'refresh');
        // Navigator.pop(context);
      } else {
        resetFields();
      }
    } catch (e) {
      _errorMessage = 'Failed to add or update product: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future insertRawMaterials(String productId) async {
    try {
      if (_rawMaterialDao != null) {
        for (InventoryDto resp in recipeData1) {
          // Check if RawMaterial with the same productId exists
          bool exists = await _rawMaterialDao!.existsWithProductId(resp.rawMId!, productId);

          // Only insert if it doesn't exist
          if (!exists) {
            await _rawMaterialDao?.insert(RawMaterialDto(
              productId: productId,
              rawMaterialId: resp.rawMId,
              inventoryId: resp.inventoryId,
              name: "${resp.inventoyProductName}//${nameController.text}",
              unit: resp.inventoryUnit,
              quantity: double.tryParse(resp.inventoryQunatity ?? '0.0') ?? 0.0,
              status: 1,
              rowStates: 1,
              unitPrice: 0,
            ));
          } else {
            for (var resp in recipeData1) {
              await _rawMaterialDao!.updateQuantity(productId, resp.rawMId!,
                  double.tryParse(resp.inventoryQunatity ?? '0.0') ?? 0.0, resp.inventoryUnit!);
            }
            log('Record with productId $productId already exists. Skipping insert.');
          }
        }
      }
    } catch (e, stackTrace) {
      log('Error in insertRawMaterials: $e');
      log('StackTrace: $stackTrace');
      // Optionally show a Snackbar or a UI alert for users
    }
  }

  Future<void> saveAddProduct(ProductDto product) async {
    _setLoading(true);
    try {
      if (_productDAO != null) {
        await _productDAO!.insertProduct(product);
      } else {
        _errorMessage = 'Database not initialized';
      }
    } catch (e) {
      _errorMessage = 'Failed to add product: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateProduct(ProductDto product) async {
    _setLoading(true);
    try {
      if (_productDAO != null) {
        await _productDAO!.updateProduct(product); // Assuming updateProduct() is defined in your DAO
      } else {
        _errorMessage = 'Database not initialized';
      }
    } catch (e) {
      _errorMessage = 'Failed to update product: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deleteProduct(int id) async {
    _setLoading(true);
    try {
      if (_productDAO != null) {
        await _productDAO!.deleteProduct(id);
      } else {
        _errorMessage = 'Database not initialized';
      }
    } catch (e) {
      _errorMessage = 'Failed to delete product: $e';
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  resetFields() {
    _categoriesList.clear();
    _productCategoryList.clear();
    categoryNamwController.clear();
    nameController.clear();
    priceController.clear();
    gstController.clear();
    priceController.clear();
    discountController.clear();
    copController.clear();
    descriptionController.clear();
    parcelAmountController.clear();
    _recipeData1.clear();
    _imageFile = null;
    selectedProductId = null;
    selectedProductUId = null;
  }

  ///////////////////////////////////////////////////////////////////////////////////
  ///
  /// product category
  ///
  ///
  ProductCategoryDao? _producCategorytDAO;

  final _uuid = const Uuid();

  List<ProductCategoryDto> _productCategoryList = [];
  List<ProductCategoryDto> get productCategoryList => _productCategoryList;

  List<DropdownItem> _categoriesList = [];
  List<DropdownItem> get categoriesList => _categoriesList;

  DropdownItem? _selectedCategory;
  DropdownItem? get selectedCategory => _selectedCategory;

  final TextEditingController categoryNamwController = TextEditingController();

  String? _errorCategoryMessage;
  String? get errorCategoryMessage => _errorCategoryMessage;

  void onChageCat(DropdownItem value) {
    _selectedCategory = value;
    notifyListeners();
  }

  Future<void> saveCategory() async {
    _producCategorytDAO = await AppDatabase().productCategoryDao;
    final newCategory = ProductCategoryDto(
      shopId: MyApp.shopId,
      categoryId: _uuid.v4(),
      productCategoryName: categoryNamwController.text.trim(),
      createdDate: DateTime.now().toIso8601String(),
      updatedDate: '',
      status: 1,
      rowStatus: 1,
    );

    await _producCategorytDAO?.insertProductCategory(newCategory);

    await loadCategories(); // refresh list
    clearCategoryForm();
  }

  Future<void> loadCategories() async {
    final list = await _producCategorytDAO!.getAllProductCategories();
    _productCategoryList = list;

    _categoriesList = [
      DropdownItem(id: '1', label: "None"),
      ..._productCategoryList.map((item) => DropdownItem(
            id: item.categoryId ?? '',
            label: item.productCategoryName ?? '',
          )),
    ];

    notifyListeners();
  }

  void clearCategoryForm() {
    categoryNamwController.clear();
    _selectedCategory = _categoriesList.first;
    notifyListeners();
  }

  bool validateCategoryForm() {
    if (categoryNamwController.text.trim().isEmpty) {
      _errorCategoryMessage = "Category name is required.";
      notifyListeners();
      return false;
    }
    _errorCategoryMessage = null;
    notifyListeners();
    return true;
  }

  RawMaterialDao? _rawMaterialDao;
  InventoryDao? _inventoryDAO;
  List<InventoryDto> _inventoryList = [];
  final List<InventoryDto> _recipeData1 = [];

  List<InventoryDto> get inventoryList => _inventoryList;

  /// Get all available ingredients including both raw inventory and converted items
  List<InventoryDto> get allAvailableIngredients {
    final List<InventoryDto> allIngredients = [];

    for (final inventory in _inventoryList) {
      // Add raw inventory if it has stock
      if (inventory.rawQuantity > 0) {
        allIngredients.add(inventory);
      }

      // Add converted item if it exists and has stock
      if (inventory.isConvertible == true &&
          inventory.convertedQuantity > 0 &&
          inventory.outputItemName != null) {
        // Create a virtual inventory item for the converted product
        final convertedItem = InventoryDto(
          id: inventory.id,
          shopId: inventory.shopId,
          inventoryId: '${inventory.inventoryId}_converted',
          inventoyProductName: inventory.convertedItemDisplayName,
          inventoryQunatity: inventory.convertedStock,
          inventoryUnit: inventory.outputUnit,
          createdDate: inventory.createdDate,
          updatedDate: inventory.updatedDate,
          status: inventory.status,
          rowStatus: inventory.rowStatus,
          isConvertible: false, // Converted items are not convertible again
          rawMId: inventory.inventoryId, // Link back to original raw material
        );
        allIngredients.add(convertedItem);
      }
    }

    return allIngredients;
  }

  List<InventoryDto> get recipeData1 => _recipeData1;

  addRecipe(InventoryDto value4) async {
    try {
      // Check if the recipe already exists by inventoryId
      final existingIndex = _recipeData1.indexWhere((item) => item.inventoryId == value4.inventoryId);
      if (existingIndex != -1) {
        // Update existing recipe
        _recipeData1[existingIndex] = value4;
      } else {
        // Add new recipe
        _recipeData1.add(value4);
      }

      // Notify listeners after making changes to the recipe data
      notifyListeners();
    } catch (e) {
      // Handle any errors that occur during the operation
      log("Error adding recipe: $e");
      // Optionally, you can use some other error handling logic or show a message to the user
    }
  }

  Future loadInventories() async {
    _inventoryList.clear();
    var list = await _inventoryDAO?.getAllInventoryList();
    _inventoryList = list ?? [];

    notifyListeners();
  }

  Future getAllRawMetrialsByProductId(String pId) async {
    List<RawMaterialDto> list = await _rawMaterialDao?.getAllRawmetrialByProductIdFromDB(pId) ?? [];

    for (RawMaterialDto rM in list) {
      await addRecipe(InventoryDto(
        rawMId: rM.rawMaterialId,
        shopId: MyApp.shopId,
        inventoryId: rM.inventoryId,
        inventoryQunatity: rM.quantity.toString(),
        inventoryUnit: rM.unit,
        status: 1,
      ));
    }
  }

  /// report
}
