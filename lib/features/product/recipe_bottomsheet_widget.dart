import 'dart:developer';

import 'package:coffee_cofe/main.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // Import intl for number formatting
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import '../../core/styles.dart';
import '../../database/entities/inventory_category_dto.dart';
import '../../widgets/common_dropdown.dart';
import '../../widgets/input_form_field.dart';
import 'product_provider.dart';

class InventoryBottomSheet extends StatefulWidget {
  final List<InventoryDto>? existingRecipe;

  const InventoryBottomSheet({super.key, this.existingRecipe});

  @override
  State<InventoryBottomSheet> createState() => _InventoryBottomSheetState();
}

class _InventoryBottomSheetState extends State<InventoryBottomSheet> {
  final _searchController = TextEditingController();
  final _quantityControllers = <InventoryDto, TextEditingController>{};
  final _focusNodes = <InventoryDto, FocusNode>{};
  final Map<String, String> selectedUnits = {};
  final uuid = const Uuid();

  String _searchQuery = '';

  final List<DropdownItem> units = [
    DropdownItem(id: 'ml', label: 'ml'),
    DropdownItem(id: 'gram', label: 'gram'),
    DropdownItem(id: 'ounce', label: 'ounce'),
    DropdownItem(id: 'liter', label: 'liter'),
    DropdownItem(id: 'piece', label: 'piece'),
    DropdownItem(id: 'no', label: 'no'),
  ];

  final Map<String, List<String>> unitRelations = {
    'liter': ['liter', 'ml'],
    'ml': ['liter', 'ml'],
    'gram': ['gram', 'kg', 'ounce'],
    'kg': ['kg', 'gram', 'ounce'],
    'ounce': ['ounce', 'gram', 'kg'],
    'no': ['no', 'piece'],
    'piece': ['no', 'piece'],
    'pack': ['pack'],
  };

  @override
  void initState() {
    super.initState();
    widget.existingRecipe?.forEach((item) {
      _quantityControllers[item] = TextEditingController(text: item.inventoryQunatity ?? '0');
      _focusNodes[item] = FocusNode();
      if (item.inventoryUnit != null && item.inventoryId != null) {
        selectedUnits[item.inventoryId!] = item.inventoryUnit!;
        log(selectedUnits[item.inventoryId!].toString());
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    for (var ctrl in _quantityControllers.values) {
      ctrl.dispose();
    }
    for (var node in _focusNodes.values) {
      node.dispose();
    }
    super.dispose();
  }

  List<DropdownItem> getFilteredUnits(String? baseUnit) {
    if (baseUnit == null) return [];
    final related = unitRelations[baseUnit.toLowerCase()] ?? [];
    return units.where((u) => related.contains(u.id.toLowerCase())).toList();
  }

  void handleUnitChange(String inventoryId, DropdownItem? value) {
    if (value != null) {
      setState(() {
        selectedUnits[inventoryId] = value.id;
      });
    }
  }

  String formatQuantity(double value) =>
      value == value.toInt() ? value.toInt().toString() : NumberFormat('0.##').format(value);

  void handleSave() {
    final List<InventoryDto> finalRecipes = [];

    for (var entry in _quantityControllers.entries) {
      final item = entry.key;
      final ctrl = entry.value;

      finalRecipes.add(
        InventoryDto(
          rawMId: item.rawMId ?? uuid.v4(),
          shopId: MyApp.shopId,
          inventoryId: item.inventoryId,
          inventoryQunatity: ctrl.text,
          inventoyProductName: item.inventoyProductName,
          inventoryUnit: selectedUnits[item.inventoryId],
        ),
      );
    }

    Navigator.pop(context, finalRecipes);
  }

  @override
  Widget build(BuildContext context) {
    final isUpdate = widget.existingRecipe?.isNotEmpty ?? false;

    return SafeArea(
      child: FractionallySizedBox(
        heightFactor: 0.95,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              /// --- Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    isUpdate ? 'Update Recipe' : 'Select Recipe Item',
                    style: black22w500.copyWith(fontWeight: FontWeight.bold),
                  ),
                  InkWell(
                    onTap: () => showTipDialog(context),
                    child: const Icon(Icons.info_outline_rounded),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              /// --- Search Field
              InputFormField(
                controller: _searchController,
                onChanged: (val) => setState(() => _searchQuery = val.toLowerCase()),
                decoration: InputDecoration(
                  hintText: 'Search Inventory...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                ),
              ),

              const SizedBox(height: 16),

              /// --- List View
              Expanded(
                child: Consumer<ProductProvider>(
                  builder: (context, provider, _) {
                    log(" isUpdate $isUpdate");
                    log(widget.existingRecipe!.length.toString());
                    final list = (isUpdate ? widget.existingRecipe! : provider.allAvailableIngredients)
                        .where(
                            (item) => (item.inventoyProductName ?? '').toLowerCase().contains(_searchQuery))
                        .toList();

                    return ListView.builder(
                      itemCount: list.length,
                      itemBuilder: (_, index) {
                        final item = list[index];
                        final id = item.inventoryId ?? '';
                        final name = provider.inventoryList
                                .firstWhereOrNull((inventL) => inventL.inventoryId == item.inventoryId)
                                ?.inventoyProductName ??
                            "";

                        _quantityControllers.putIfAbsent(
                            item, () => TextEditingController(text: isUpdate ? item.inventoryQunatity : '0'));
                        _focusNodes.putIfAbsent(item, () => FocusNode());

                        final filteredUnits = getFilteredUnits(item.inventoryUnit);
                        final unit = filteredUnits.firstWhereOrNull(
                              (u) => u.id.toLowerCase() == (item.inventoryUnit ?? '').toLowerCase(),
                            ) ??
                            (filteredUnits.isNotEmpty ? filteredUnits.first : null);

                        if (unit != null && selectedUnits[id] == null) {
                          selectedUnits[id] = unit.id;
                        }

                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12.0),
                          child: Card(
                            elevation: 4,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                            child: Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: Row(
                                children: [
                                  Expanded(flex: 1, child: Text(name, style: black16w500)),
                                  const SizedBox(width: 5),
                                  Expanded(
                                    flex: 1,
                                    child: InputFormField(
                                      controller: _quantityControllers[item]!,
                                      focusNode: _focusNodes[item],
                                      hint: 'Qty',
                                      inputType: const TextInputType.numberWithOptions(decimal: true),
                                      onFieldSubmitted: (val) {
                                        final qty = double.tryParse(val);
                                        if (qty != null && qty >= 0) {
                                          _quantityControllers[item]!.text = formatQuantity(qty);
                                        }
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 5),
                                  SizedBox(
                                    width: 130,
                                    height: 63,
                                    child: CommonDropdown(
                                      items: filteredUnits,
                                      hitLabel: 'Select Unit',
                                      selectedItem:
                                          filteredUnits.firstWhereOrNull((u) => u.id == selectedUnits[id]),
                                      isSearchable: false,
                                      onChanged: (u) => handleUnitChange(id, u),
                                      // itemLabel: (DropdownItem item) => item.label,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  },
                ),
              ),

              /// --- Save Button
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: handleSave,
                child: const Text("Save Recipe"),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

void showTipDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: const Text(
          '📏 Measurement Units Quick Tips',
          style: TextStyle(
            fontSize: 18, // Increased font size for title
            color: Colors.blue, // Title color
          ),
        ),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '✅ Grams (g) → Small solid weights (sugar, coffee powder).',
                style: TextStyle(fontSize: 16, color: Colors.black87),
              ),
              Text(
                '✅ Milliliters (ml) → Small liquid volumes (milk, oil).',
                style: TextStyle(fontSize: 16, color: Colors.black87),
              ),
              Text(
                '✅ Ounce (oz) → Small solid or liquid measurements.',
                style: TextStyle(fontSize: 16, color: Colors.black87),
              ),
              Text(
                '   - 1 oz ≈ 28.35g (solid)',
                style: TextStyle(fontSize: 16, color: Colors.black54),
              ),
              Text(
                '   - 1 oz ≈ 29.57ml (liquid)',
                style: TextStyle(fontSize: 16, color: Colors.black54),
              ),
              Text(
                '✅ Liters (L) → Large liquid volumes (water, milk).',
                style: TextStyle(fontSize: 16, color: Colors.black87),
              ),
              Text(
                '✅ Piece (pcs) → Countable items (apple, cup).',
                style: TextStyle(fontSize: 16, color: Colors.black87),
              ),
              Text(
                '✅ Pack → Grouped items (pack of biscuits).',
                style: TextStyle(fontSize: 16, color: Colors.black87),
              ),
              SizedBox(height: 16),
              Text(
                'Mini Cheatsheet:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.black),
              ),
              Text(
                '   - 1 L = 1000 ml',
                style: TextStyle(fontSize: 16, color: Colors.black87),
              ),
              Text(
                '   - 1 kg = 1000 g',
                style: TextStyle(fontSize: 16, color: Colors.black87),
              ),
              Text(
                '   - 1 oz ≈ 28.35 g',
                style: TextStyle(fontSize: 16, color: Colors.black87),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Close', style: TextStyle(fontSize: 16, color: Colors.blue)),
          ),
        ],
      );
    },
  );
}

class ProductWithUnits {
  final String productId;
  final List<DropdownItem> availableUnits;
  DropdownItem? selectedUnit;

  ProductWithUnits({
    required this.productId,
    required this.availableUnits,
    this.selectedUnit,
  });
}
