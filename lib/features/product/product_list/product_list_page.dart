import 'dart:io';

import 'package:coffee_cofe/core/constants/images.dart';
import 'package:coffee_cofe/widgets/common_appbar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../widgets/common_drawer.dart';
import '../../profile/profile_settings_controller.dart';
import '../add_product_screen.dart';
import '../product_provider.dart';
import 'product_list_provider.dart';

class ProductListPage extends StatefulWidget {
  const ProductListPage({super.key});

  @override
  State<ProductListPage> createState() => _ProductListPageState();
}

class _ProductListPageState extends State<ProductListPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      initFunction(); // safe to call now
    });
  }

  initFunction() async {
    await Provider.of<ProductListProvider>(context, listen: false).fetchProducts();
    if (mounted) {
      await Provider.of<ProductProvider>(context, listen: false).initFunction();
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<Color>(
      valueListenable: ProfileController.themeColorNotifier,
      builder: (context, bgColor, child) {
        return Scaffold(
          drawer: const CommonDrawer(),
          floatingActionButton: Padding(
            padding: const EdgeInsets.only(left: 10.0, bottom: 25, right: 25),
            child: FloatingActionButton(
              backgroundColor: AppColors.primary,
              onPressed: () async {
                final provider = Provider.of<ProductListProvider>(context, listen: false);
                final result = await Get.to(() => const ProductScreen());
                if (result == 'refresh' && mounted) {
                  await provider.fetchProducts();
                }
              },
              child: const Icon(
                Icons.add,
                size: 35,
                color: Colors.white,
              ),
            ),
          ),
          appBar: CommonAppBar(
            title: 'Products',
            appBarColor: bgColor,
          ),
          body: Consumer2<ProductListProvider, ProductProvider>(
            builder: (context, providerProductList, productProvider, child) {
              if (providerProductList.isLoading) {
                return const Center(child: CircularProgressIndicator());
              } else if (providerProductList.errorMessage != null) {
                return Center(child: Text(providerProductList.errorMessage!));
              } else if (providerProductList.products.isEmpty) {
                return const Center(child: Text("No products available"));
              }

              return ListView.builder(
                itemCount: providerProductList.products.length,
                itemBuilder: (context, index) {
                  final product = providerProductList.products[index];

                  return InkWell(
                    onTap: () async {
                      final provider = Provider.of<ProductListProvider>(context, listen: false);
                      final result = await Get.to(() => ProductScreen(
                            selectedIndex: index,
                          ));

                      if (result == 'refresh' && mounted) {
                        await provider.fetchProducts();
                      }
                      // Get.toNamed(AppRoutes.productsScreen);
                    },
                    child: Card(
                      elevation: 4,
                      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            // Product Image
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: (product.productImagePath != null &&
                                      product.productImagePath!.isNotEmpty &&
                                      File(product.productImagePath!).existsSync())
                                  ? Image.file(
                                      File(product.productImagePath!),
                                      width: 80,
                                      height: 80,
                                      fit: BoxFit.cover,
                                    )
                                  : Image.asset(
                                      noImage,
                                      width: 80,
                                      height: 80,
                                      fit: BoxFit.cover,
                                    ),
                            ),
                            const SizedBox(width: 12),

                            // Product Info
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Product Name
                                  Text(
                                    product.productName ?? 'Unnamed Product',
                                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black87,
                                        ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),

                                  // Product Category
                                  Text(
                                    'Category: ${productProvider.categoriesList.firstWhereOrNull((cat) => cat.id == product.categoryId)?.label ?? "Unknown"}',
                                    style: const TextStyle(
                                      color: Colors.grey,
                                      fontSize: 14,
                                    ),
                                  ),
                                  const SizedBox(height: 4),

                                  // Price and S.No
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        '₹ ${product.costOfProduction ?? "0.00"}',
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                              fontWeight: FontWeight.bold,
                                              color: Colors.green,
                                            ),
                                      ),
                                      Text(
                                        'S.No: ${index + 1}',
                                        style: const TextStyle(
                                          color: Colors.grey,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            },
          ),
        );
      },
    );
  }
}
