import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../config/app_config.dart';
import '../../services/mock_auth_service.dart';
import 'login_provider.dart';

/// Demo screen to show available mock users for testing
class MockUsersDemo extends StatelessWidget {
  const MockUsersDemo({super.key});

  @override
  Widget build(BuildContext context) {
    if (!AppConfig.useMockAuth || !AppConfig.showMockUserList) {
      return const SizedBox.shrink();
    }

    return Flexible(
      child: SingleChildScrollView(
        child: Card(
          margin: const EdgeInsets.all(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.info_outline, color: Colors.blue),
                    const SizedBox(width: 8),
                    Text(
                      'Demo Users (Development Mode)',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Tap any user card below to automatically fill the login form:',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        final loginProvider = Provider.of<LoginProvider>(context, listen: false);
                        loginProvider.clearForm();
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Login form cleared'),
                            duration: Duration(seconds: 1),
                            backgroundColor: Colors.orange,
                          ),
                        );
                      },
                      icon: const Icon(Icons.clear, size: 16),
                      label: const Text('Clear'),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.orange,
                        textStyle: const TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: Colors.green.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green.shade600, size: 16),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          'All demo credentials are fully functional and tested',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.green.shade700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // Quick Access Section
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.flash_on, color: Colors.blue.shade600, size: 16),
                          const SizedBox(width: 6),
                          Text(
                            'Quick Access',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: _buildQuickAccessButton(
                              context,
                              'Demo User',
                              '<EMAIL>',
                              'demo123',
                              Icons.person,
                              Colors.green,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _buildQuickAccessButton(
                              context,
                              'Test User',
                              '<EMAIL>',
                              'test',
                              Icons.bug_report,
                              Colors.orange,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                Text(
                  'All Demo Users',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade700,
                      ),
                ),
                const SizedBox(height: 8),
                ...MockAuthService.getAllMockUsers().map((user) => _buildUserCard(context, user)),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.verified, color: Colors.green.shade700, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            'Production-Level Demo Data',
                            style: TextStyle(
                              color: Colors.green.shade700,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Each demo login automatically loads comprehensive business data:',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 4),
                      ...[
                        '• 50+ Products with categories & pricing',
                        '• Inventory with conversion tracking',
                        '• 30 days of sales history & transactions',
                        '• Discount configurations & coupons',
                        '• GST settings & tax configurations',
                        '• Workspace settings & user permissions'
                      ].map(
                        (feature) => Padding(
                          padding: const EdgeInsets.only(left: 8, top: 2),
                          child: Text(
                            feature,
                            style: TextStyle(
                              color: Colors.green.shade600,
                              fontSize: 11,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'No difference between demo and real login experience!',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserCard(BuildContext context, Map<String, dynamic> user) {
    final identifier = user['emailAddress'] ?? user['mobileNo'] ?? '';
    final password = _getPasswordForUser(identifier);
    final role = user['role'] ?? 'user';
    final permissions = user['permissions'] ?? 'basic';
    final department = user['department'] ?? 'General';

    // Define role colors and icons
    final roleConfig = _getRoleConfig(role);

    return InkWell(
      onTap: () {
        // Fill the login form with this user's credentials
        final loginProvider = Provider.of<LoginProvider>(context, listen: false);
        loginProvider.fillDemoCredentials(identifier, password);

        // Show feedback to user
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Filled credentials for ${user['userName']} - Production data will be loaded!'),
            duration: const Duration(seconds: 3),
            backgroundColor: Colors.green,
          ),
        );
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 18,
                  backgroundColor: roleConfig['color'],
                  child: Icon(
                    roleConfig['icon'],
                    color: Colors.white,
                    size: 18,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user['userName'] ?? 'Unknown',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: roleConfig['color'].withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: roleConfig['color'].withOpacity(0.3)),
                            ),
                            child: Text(
                              roleConfig['label'],
                              style: TextStyle(
                                color: roleConfig['color'],
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            department,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 10,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.storage,
                            size: 12,
                            color: Colors.green.shade700,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'PRODUCTION DATA',
                            style: TextStyle(
                              color: Colors.green.shade700,
                              fontSize: 9,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.touch_app,
                            size: 12,
                            color: Colors.blue.shade700,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            'TAP TO FILL',
                            style: TextStyle(
                              color: Colors.blue.shade700,
                              fontSize: 8,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            _buildCredentialRow(
              context,
              'Email/Mobile',
              user['emailAddress'] ?? user['mobileNo'] ?? 'N/A',
            ),
            _buildCredentialRow(
              context,
              'Password',
              _getPasswordForUser(user['emailAddress'] ?? user['mobileNo'] ?? ''),
            ),
            const SizedBox(height: 6),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.security,
                        size: 14,
                        color: Colors.blue.shade700,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Access Level: ${roleConfig['description']}',
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Permissions: ${_getPermissionDescription(permissions)}',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.blue.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCredentialRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                Clipboard.setData(ClipboardData(text: value));
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Copied: $value'),
                    duration: const Duration(seconds: 1),
                  ),
                );
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        value,
                        style: const TextStyle(
                          fontSize: 12,
                          fontFamily: 'monospace',
                        ),
                      ),
                    ),
                    Icon(
                      Icons.copy,
                      size: 12,
                      color: Colors.grey.shade400,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getPasswordForUser(String identifier) {
    // This is just for demo purposes - in real app, passwords should never be exposed
    final mockUsers = {
      // Professional Coffee Shop Demo Credentials
      '<EMAIL>': 'manager123',
      '9876543210': 'manager123',
      '<EMAIL>': 'owner2024',
      '9123456789': 'owner2024',
      '<EMAIL>': 'barista123',
      '9234567890': 'barista123',
      '<EMAIL>': 'cashier123',
      '9345678901': 'cashier123',
      '<EMAIL>': 'demo123',
      '9000000000': 'demo123',
      '<EMAIL>': 'test',
      '1111111111': 'test',
    };
    return mockUsers[identifier] ?? 'demo123';
  }

  Map<String, dynamic> _getRoleConfig(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return {
          'color': Colors.red,
          'icon': Icons.admin_panel_settings,
          'label': 'Admin',
          'description': 'Full system access',
        };
      case 'employee':
        return {
          'color': Colors.blue,
          'icon': Icons.work,
          'label': 'Employee',
          'description': 'Staff access',
        };
      default:
        return {
          'color': Colors.grey,
          'icon': Icons.person,
          'label': 'User',
          'description': 'Basic access',
        };
    }
  }

  Widget _buildQuickAccessButton(
    BuildContext context,
    String name,
    String identifier,
    String password,
    IconData icon,
    Color color,
  ) {
    return ElevatedButton.icon(
      onPressed: () {
        final loginProvider = Provider.of<LoginProvider>(context, listen: false);
        loginProvider.fillDemoCredentials(identifier, password);

        // Show feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Filled credentials for $name'),
            duration: const Duration(seconds: 2),
            backgroundColor: color,
          ),
        );
      },
      icon: Icon(icon, size: 16),
      label: Text(
        name,
        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withOpacity(0.1),
        foregroundColor: color,
        elevation: 0,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
          side: BorderSide(color: color.withOpacity(0.3)),
        ),
      ),
    );
  }
}
