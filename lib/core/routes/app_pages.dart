// lib/routes/app_pages.dart

import 'package:coffee_cofe/features/inventory/inventory_screen.dart';
import 'package:get/get.dart';
import '../../features/attendance/attendance_screen.dart';
import '../../features/auth/login_screen.dart';
import '../../features/auth/signup/signu_screen.dart';
import '../../features/bill/billing_screen.dart';
import '../../features/employee/employee_list/employee_list_screen.dart';
import '../../features/home/<USER>';
import '../../features/main_screen.dart';
import '../../features/product/product_list/product_list_page.dart';
import '../../features/profile/profile_screen.dart';
import '../../features/report/product_report/product_report_screen.dart';
import '../../features/report/sales_report/reportscreen.dart';
import '../../features/splash_screen.dart';
import '../../screens/database_export_screen.dart';
import '../../screens/gst_configuration_screen.dart';
import '../../screens/simple_gst_config_screen.dart';
import 'app_routes.dart';

class AppPages {
  static final routes = [
    GetPage(name: AppRoutes.splash, page: () => const SplashScreen()),
    GetPage(
      name: AppRoutes.login,
      page: () => const LoginScreen(),
    ),
    GetPage(
      name: AppRoutes.home,
      page: () => const HomeScreen(),
    ),
    GetPage(
      name: AppRoutes.mainScreen,
      page: () => const MainScreen(),
    ),
    GetPage(
      name: AppRoutes.signup,
      page: () => const RegisterScreen(),
    ),
    GetPage(
      name: AppRoutes.billingScreen,
      page: () => const BillingScreen(),
    ),
    GetPage(
      name: AppRoutes.productsScreen,
      page: () => const ProductListPage(),
    ),
    GetPage(
      name: AppRoutes.inventoryScreen,
      page: () => const InventoryScreen(),
    ),
    GetPage(
      name: AppRoutes.profileScreen,
      page: () => ProfileScreen(),
    ),
    GetPage(
      name: AppRoutes.salesReportScreen,
      page: () => const SalesReportScreen(),
    ),
    GetPage(
      name: AppRoutes.attendanceScreen,
      page: () => const AttendanceScreen(),
    ),
    GetPage(
      name: AppRoutes.employeeListScreen,
      page: () => const EmployeeListScreen(),
    ),
    GetPage(
      name: AppRoutes.productReportScreen,
      page: () => const ProductReportScreen(),
    ),
    GetPage(
      name: AppRoutes.databaseExportScreen,
      page: () => const DatabaseExportScreen(),
    ),
    GetPage(
      name: AppRoutes.gstConfigurationScreen,
      page: () => const GSTConfigurationScreen(),
    ),
    GetPage(
      name: AppRoutes.simpleGstConfigScreen,
      page: () => const SimpleGSTConfigScreen(),
    ),
  ];
}
