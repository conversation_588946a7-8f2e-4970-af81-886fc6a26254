/// Migration script to simplify GST system
/// This migration helps transition from complex GST to simplified GST
library;

const String createSimpleGSTConfigTable = '''
  CREATE TABLE IF NOT EXISTS SimpleGSTConfig (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    businessName TEXT NOT NULL,
    gstin TEXT DEFAULT NULL,
    defaultGSTRate REAL DEFAULT 18.0,
    enableGST INTEGER DEFAULT 1,
    createdDate TEXT DEFAULT CURRENT_TIMESTAMP,
    updatedDate TEXT DEFAULT CURRENT_TIMESTAMP
  );
''';

const String insertDefaultSimpleGSTConfig = '''
  INSERT OR IGNORE INTO SimpleGSTConfig (
    businessName, 
    gstin, 
    defaultGSTRate, 
    enableGST
  ) VALUES (
    'My Business', 
    NULL, 
    18.0, 
    1
  );
''';

/// Migration to add simplified GST support
/// This creates a simple GST configuration table alongside the existing complex one
/// Users can choose which system to use
List<String> getSimpleGSTMigrationScripts() {
  return [
    createSimpleGSTConfigTable,
    insertDefaultSimpleGSTConfig,
  ];
}

/// Helper function to migrate from complex to simple GST
/// This extracts basic information from the complex GST configuration
const String migrateComplexToSimpleGST = '''
  INSERT OR REPLACE INTO SimpleGSTConfig (
    businessName,
    gstin,
    defaultGSTRate,
    enableGST,
    createdDate,
    updatedDate
  )
  SELECT 
    businessName,
    businessGSTIN,
    defaultGSTRate,
    1,
    datetime('now'),
    datetime('now')
  FROM GSTConfiguration 
  WHERE isActive = 1
  LIMIT 1;
''';

/// Function to perform the migration from complex to simple GST
List<String> getMigrationFromComplexToSimpleScripts() {
  return [
    createSimpleGSTConfigTable,
    migrateComplexToSimpleGST,
  ];
}
