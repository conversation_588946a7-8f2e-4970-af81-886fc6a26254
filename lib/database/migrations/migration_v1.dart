import '../table_columns.dart';

const String createProductTable = '''
CREATE TABLE $productsTable (
  $id INTEGER PRIMARY KEY AUTOINCREMENT,
  $dbShopUId TEXT NOT NULL,
  $productId TEXT UNIQUE NOT NULL,
  $productUniqId TEXT UNIQUE,
  $productName TEXT NOT NULL,
  $createdDate TEXT DEFAULT NULL,
  $updatedDate TEXT DEFAULT NULL,
  $productImage TEXT DEFAULT NULL,
  $discountAmount TEXT DEFAULT NULL,
  $status INTEGER DEFAULT NULL,
  $rowStatus INTEGER DEFAULT NULL,
  $price TEXT DEFAULT NULL,
  $mrp TEXT DEFAULT NULL,
  $cgst TEXT DEFAULT NULL,
  $sgst TEXT DEFAULT NULL,
  $productionCost TEXT DEFAULT NULL,
  $categoryId TEXT DEFAULT NULL,
  $toppingsId TEXT DEFAULT NULL,
  $recipeRawMaterialId TEXT DEFAULT NULL
);
''';

const String createSettingsTable = '''
CREATE TABLE $settingsTable (
  $id INTEGER PRIMARY KEY AUTOINCREMENT,
  $dbShopUId TEXT UNIQUE NOT NULL,
  $dbKeyName TEXT UNIQUE NOT NULL,
  $dbValue TEXT NOT NULL
);
''';

const String createSalesTable = '''
  CREATE TABLE $salesTable (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $salesId TEXT DEFAULT NULL,
    $salesTransactionId TEXT DEFAULT NULL,
    $createdDate TEXT DEFAULT NULL,
    $note TEXT DEFAULT NULL,
    $updatedDate TEXT DEFAULT NULL,
    $totalAmount TEXT DEFAULT NULL,
    $status INTEGER DEFAULT NULL,
    $rowStatus INTEGER DEFAULT NULL,
    $customerId TEXT DEFAULT NULL
  )
''';

const String createSalesTransactionTable = '''
  CREATE TABLE $salesTransactionTable (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $salesId TEXT DEFAULT NULL,
    $salesTransactionId TEXT DEFAULT NULL,
    $productId TEXT DEFAULT NULL,
    $productName TEXT DEFAULT NULL,
    $productAmount TEXT DEFAULT NULL,
    $cgst TEXT DEFAULT NULL,
    $sgst TEXT DEFAULT NULL,
    $parcelAmount TEXT DEFAULT NULL,
    $discountAmount TEXT DEFAULT NULL,
    $productQty INTEGER DEFAULT NULL,
    $status INTEGER DEFAULT NULL,
    $rowStatus INTEGER DEFAULT NULL,
    $createdAt TEXT DEFAULT CURRENT_TIMESTAMP
  )
''';

const String createProductCategoryTable = '''
CREATE TABLE $productCategoryTable (
  $id INTEGER PRIMARY KEY AUTOINCREMENT,
  $dbShopUId TEXT NOT NULL,
  $categoryId TEXT DEFAULT NULL,
  $productCategoryName TEXT DEFAULT NULL,
  $createdDate TEXT DEFAULT NULL,
  $updatedDate TEXT DEFAULT NULL,
  $status INTEGER DEFAULT NULL,
  $rowStatus INTEGER DEFAULT NULL
);
''';

const String createRawMetrialTable = '''
CREATE TABLE $rawMaterialTable (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
  $recipeRawMaterialId UNIQUE,
  $productId  TEXT DEFAULT NULL,
   $inventoryId TEXT DEFAULT NULL,
  $rawMaterialName TEXT DEFAULT NULL,
  $rawMaterialUnit TEXT DEFAULT NULL,
  $rawMaterialUnitPrice REAL,
  $status INTEGER,
  $rowStatus INTEGER,
  $rawMaterialQuantity TEXT DEFAULT NULL
)
''';

const String createInventoryTable = '''
    CREATE TABLE $inventoryTable (
       $id INTEGER PRIMARY KEY AUTOINCREMENT,
      $dbShopUId TEXT NOT NULL,
      $inventoryId TEXT NOT NULL,
      $inventoyProductName TEXT DEFAULT NULL,
      $inventoryQunatity TEXT DEFAULT NULL,
      $inventoryUnit TEXT DEFAULT NULL,
      $createdDate TEXT DEFAULT NULL,
      $updatedDate TEXT DEFAULT NULL,
      $status INTEGER DEFAULT NULL,
      $rowStatus INTEGER DEFAULT NULL,
      $isConvertible INTEGER DEFAULT 0,
      $outputItemName TEXT DEFAULT NULL,
      $outputQuantity TEXT DEFAULT NULL,
      $outputUnit TEXT DEFAULT NULL,
      $conversionRatio TEXT DEFAULT NULL,
      $convertedStock TEXT DEFAULT '0'
    )
''';

const String createDiscountTable = '''
  CREATE TABLE $discountTable (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $discountID TEXT NOT NULL,
    $discountCouponName TEXT DEFAULT NULL,
    $discountMode TEXT DEFAULT NULL,
    $discountFormula TEXT DEFAULT NULL,
    $discountCategoryID TEXT DEFAULT NULL,
    $discountCreatedDate TEXT DEFAULT NULL,
    $discountCreatedBy TEXT DEFAULT NULL,
    $discountActive INTEGER DEFAULT NULL,
    $discountSync INTEGER NOT NULL,
    $workSpaceId TEXT NOT NULL,
    $rowStatus INTEGER NOT NULL
  )
''';

const String createworkSpaceTable = '''
          CREATE TABLE $workSpaceSettingsTable (
            $wSSettingId INTEGER PRIMARY KEY AUTOINCREMENT,
            $workSpaceId TEXT NOT NULL,
            $wSName TEXT NOT NULL,
            $wSUserName TEXT NOT NULL,
            $wSUserId TEXT NOT NULL,
            $wSSettingKey TEXT NOT NULL,
            $wSSettingValue TEXT NOT NULL,
            $workSsettingSync INT NOT NULL
          )
          ''';

const String createDeviceSettingsTable = '''
          CREATE TABLE $deviceSettingsTable (
            $wSSettingId INTEGER PRIMARY KEY AUTOINCREMENT,
            $wSSettingKey TEXT NOT NULL,
            $wSSettingValue TEXT NOT NULL
            )
          ''';
