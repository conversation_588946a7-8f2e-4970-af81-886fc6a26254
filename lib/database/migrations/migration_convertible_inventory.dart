/// Migration script to add convertible inventory support
/// This migration adds columns for inventory conversion functionality
library;

import '../table_columns.dart';

const String addConvertibleInventoryColumns = '''
  ALTER TABLE $inventoryTable ADD COLUMN $isConvertible INTEGER DEFAULT 0;
  ALTER TABLE $inventoryTable ADD COLUMN $outputItemName TEXT DEFAULT NULL;
  ALTER TABLE $inventoryTable ADD COLUMN $outputQuantity TEXT DEFAULT NULL;
  ALTER TABLE $inventoryTable ADD COLUMN $outputUnit TEXT DEFAULT NULL;
  ALTER TABLE $inventoryTable ADD COLUMN $conversionRatio TEXT DEFAULT NULL;
  ALTER TABLE $inventoryTable ADD COLUMN $convertedStock TEXT DEFAULT '0';
''';

/// Migration function to add convertible inventory support
Future<void> migrateToConvertibleInventory(database) async {
  // Split the migration into individual statements
  final statements = addConvertibleInventoryColumns
      .split(';')
      .where((statement) => statement.trim().isNotEmpty)
      .toList();
  
  for (String statement in statements) {
    await database.execute(statement.trim() + ';');
  }
}
