// common columns
const String dbShopUId = 'shopId';
const String id = 'id';
const String createdDate = 'createdDate';
const String updatedDate = 'updatedDate';
const String rowStatus = 'rowStatus';
const String status = 'status';
const String note = 'note';
const String workSpaceId = 'workSpaceId';

// product table
const String productsTable = 'Products';
const String productId = 'productId';
const String productName = 'productName';
const String productUnit = "unit"; // e.g., "cup", "bottle"
const String productUnitPrice = "unit_price";
const String productUniqId = "uniqid"; // e.g., INV001
const String price = 'price';
const String mrp = 'mrp';
const String categoryId = 'categoryId';
const String toppingsId = 'toppingsId';
const String cgst = 'cgst';
const String sgst = 'sgst';
const String productionCost = 'prouctionCost';
const String productImage = 'productImage';

// settings table
const String settingsTable = 'Settings';
const String dbKeyName = 'keyName';
const String dbValue = 'value';

//mesurementtable
const String mesurementTable = 'Mesurement';
const String mesurementId = 'mesurementId';
const String mesurementName = 'mesurementName';
const String mesurementPrice = 'mesurementPrice';

const String salesTable = "Sales";
const String salesId = "salesId";
const String salesTransactionId = "salesTransactionId";
const String parcelAmount = "parcelAmount";

const String totalAmount = "totalAmount";

const String customerId = "customerId";

const String salesTransactionTable = "salesTransaction";
const String productAmount = "productAmount";
const String discountAmount = "discountAmount";
const String productQty = "productQty";
const String createdAt = "createdAt";

const String productCategoryTable = "ProductCategory";
const String productCategoryName = 'productCategoryName';

const String inventoryTable = "Inventory";
const String inventoryId = "inventoyId";
const String inventoyProductName = "inventoyProductName";
const String inventoryQunatity = "inventoryQunatity";
const String inventoryUnit = "inventoryUnit";

// Convertible inventory fields
const String isConvertible = "isConvertible";
const String outputItemName = "outputItemName";
const String outputQuantity = "outputQuantity";
const String outputUnit = "outputUnit";
const String conversionRatio = "conversionRatio";
const String convertedStock = "convertedStock";

// Raw Material Table (e.g., Milk, Coffee Powder, Sugar)
const String rawMaterialTable = "RawMaterial";
const String rawMaterialId = "rawMaterialId"; // e.g., RM001
const String rawMaterialName = "name"; // e.g., Milk, Coffee Powder
const String rawMaterialUnit = "unit"; // e.g., "ml", "gram"
const String rawMaterialUnitPrice = "unitPrice";

const String rawMaterialQuantity = "quantity"; // Quantity available

// Recipe Table: Links Products to Raw Materials
const String recipeTable = "Recipe";
const String recipeProductId = "productId"; // Foreign key linking to product
const String recipeRawMaterialId = "rawMaterialId"; // Foreign key linking to raw material
const String recipeQuantity = "quantity"; // Quantity of raw material needed

const String workSpaceSettingsTable = "WorkSpaceSettings";
const String deviceSettingsTable = "DeviceSettings";

const String wSUserName = "workSpaceUserName";
const String wSName = "workSpaceName";
const String wSUserId = "workSpaceUserId";
const String deviceSettings = "DeviceSettings";
const String wSSettingId = 'settingId';

const String wSSettingKey = "settingKey";
const String wSSettingValue = "settingValue";
const String workSsettingSync = "settingSync";

const discountTable = "Discount";

const discountID = "discountID";
const discountCouponName = "couponName";
const discountMode = "mode";
const discountFormula = "formula";
const discountCategoryID = "categoryID";
const discountCreatedDate = "createdDate";
const discountCreatedBy = "createdBy";
const discountActive = "discountActive";
const discountSync = "discountSync";

// Enhanced discount columns
const discountType = "discountType";
const discountCouponCode = "couponCode";
const discountMaxUsageCount = "maxUsageCount";
const discountCurrentUsageCount = "currentUsageCount";
const discountMinOrderAmount = "minOrderAmount";
const discountMaxDiscountAmount = "maxDiscountAmount";
const discountIsStackable = "isStackable";
const discountApplicableProducts = "applicableProducts";
const discountApplicableCategories = "applicableCategories";
const discountCustomerSegment = "customerSegment";
const discountPriority = "priority";
const discountDescription = "description";
const discountTermsAndConditions = "termsAndConditions";
const discountLastUsedDate = "lastUsedDate";
const discountLastUsedBy = "lastUsedBy";
const discountIsAutoApply = "isAutoApply";
const discountTriggerConditions = "triggerConditions";
