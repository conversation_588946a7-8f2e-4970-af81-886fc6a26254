import 'dart:developer';
import 'package:sqflite/sqflite.dart';
import 'app_database.dart';

/// Database repair utility to fix missing columns and schema issues
class DatabaseRepairUtility {
  static final DatabaseRepairUtility _instance = DatabaseRepairUtility._internal();
  factory DatabaseRepairUtility() => _instance;
  DatabaseRepairUtility._internal();

  /// Check if a column exists in a table
  static Future<bool> columnExists(Database db, String tableName, String columnName) async {
    try {
      final result = await db.rawQuery('PRAGMA table_info($tableName)');
      for (var column in result) {
        if (column['name'] == columnName) {
          return true;
        }
      }
      return false;
    } catch (e) {
      log('Error checking column existence: $e');
      return false;
    }
  }

  /// Add column if it doesn't exist
  static Future<void> addColumnIfNotExists(
      Database db, String tableName, String columnName, String columnDefinition) async {
    try {
      final exists = await columnExists(db, tableName, columnName);
      if (!exists) {
        await db.execute('ALTER TABLE $tableName ADD COLUMN $columnName $columnDefinition');
        log('Added column $columnName to $tableName');
      } else {
        log('Column $columnName already exists in $tableName');
      }
    } catch (e) {
      log('Error adding column $columnName to $tableName: $e');
    }
  }

  /// Repair Sales table - add missing GST columns
  static Future<void> repairSalesTable() async {
    try {
      final db = await AppDatabase().database;
      log('Repairing Sales table...');

      await addColumnIfNotExists(db, 'Sales', 'subtotalAmount', 'REAL DEFAULT NULL');
      await addColumnIfNotExists(db, 'Sales', 'cgstAmount', 'REAL DEFAULT NULL');
      await addColumnIfNotExists(db, 'Sales', 'sgstAmount', 'REAL DEFAULT NULL');
      await addColumnIfNotExists(db, 'Sales', 'igstAmount', 'REAL DEFAULT NULL');
      await addColumnIfNotExists(db, 'Sales', 'cessAmount', 'REAL DEFAULT NULL');
      await addColumnIfNotExists(db, 'Sales', 'totalTaxAmount', 'REAL DEFAULT NULL');
      await addColumnIfNotExists(db, 'Sales', 'isReverseCharge', 'INTEGER DEFAULT 0');
      await addColumnIfNotExists(db, 'Sales', 'stateCode', 'TEXT DEFAULT NULL');
      await addColumnIfNotExists(db, 'Sales', 'taxConfiguration', 'TEXT DEFAULT NULL');

      log('Sales table repair completed');
    } catch (e) {
      log('Error repairing Sales table: $e');
    }
  }

  /// Repair Discount table - add missing priority and enhanced columns
  static Future<void> repairDiscountTable() async {
    try {
      final db = await AppDatabase().database;
      log('Repairing Discount table...');

      await addColumnIfNotExists(db, 'Discount', 'priority', 'INTEGER DEFAULT 0');
      await addColumnIfNotExists(db, 'Discount', 'discountType', 'TEXT DEFAULT NULL');
      await addColumnIfNotExists(db, 'Discount', 'couponCode', 'TEXT DEFAULT NULL');
      await addColumnIfNotExists(db, 'Discount', 'maxUsageCount', 'INTEGER DEFAULT NULL');
      await addColumnIfNotExists(db, 'Discount', 'currentUsageCount', 'INTEGER DEFAULT 0');
      await addColumnIfNotExists(db, 'Discount', 'minOrderAmount', 'REAL DEFAULT NULL');
      await addColumnIfNotExists(db, 'Discount', 'maxDiscountAmount', 'REAL DEFAULT NULL');
      await addColumnIfNotExists(db, 'Discount', 'isStackable', 'INTEGER DEFAULT 0');
      await addColumnIfNotExists(db, 'Discount', 'applicableProducts', 'TEXT DEFAULT NULL');
      await addColumnIfNotExists(db, 'Discount', 'applicableCategories', 'TEXT DEFAULT NULL');
      await addColumnIfNotExists(db, 'Discount', 'customerSegment', 'TEXT DEFAULT \'all\'');
      await addColumnIfNotExists(db, 'Discount', 'description', 'TEXT DEFAULT NULL');
      await addColumnIfNotExists(db, 'Discount', 'termsAndConditions', 'TEXT DEFAULT NULL');
      await addColumnIfNotExists(db, 'Discount', 'lastUsedDate', 'TEXT DEFAULT NULL');
      await addColumnIfNotExists(db, 'Discount', 'lastUsedBy', 'TEXT DEFAULT NULL');
      await addColumnIfNotExists(db, 'Discount', 'isAutoApply', 'INTEGER DEFAULT 0');
      await addColumnIfNotExists(db, 'Discount', 'triggerConditions', 'TEXT DEFAULT NULL');

      log('Discount table repair completed');
    } catch (e) {
      log('Error repairing Discount table: $e');
    }
  }

  /// Repair SalesTransaction table - add missing GST columns
  static Future<void> repairSalesTransactionTable() async {
    try {
      final db = await AppDatabase().database;
      log('Repairing SalesTransaction table...');

      await addColumnIfNotExists(db, 'salesTransaction', 'baseAmount', 'REAL DEFAULT NULL');
      await addColumnIfNotExists(db, 'salesTransaction', 'igst', 'REAL DEFAULT NULL');
      await addColumnIfNotExists(db, 'salesTransaction', 'cess', 'REAL DEFAULT NULL');
      await addColumnIfNotExists(db, 'salesTransaction', 'gstRate', 'REAL DEFAULT NULL');
      await addColumnIfNotExists(db, 'salesTransaction', 'cessRate', 'REAL DEFAULT NULL');
      await addColumnIfNotExists(db, 'salesTransaction', 'hsnCode', 'TEXT DEFAULT NULL');
      await addColumnIfNotExists(db, 'salesTransaction', 'taxableAmount', 'REAL DEFAULT NULL');
      await addColumnIfNotExists(db, 'salesTransaction', 'totalTaxAmount', 'REAL DEFAULT NULL');
      await addColumnIfNotExists(db, 'salesTransaction', 'customTaxApplied', 'INTEGER DEFAULT 0');

      log('SalesTransaction table repair completed');
    } catch (e) {
      log('Error repairing SalesTransaction table: $e');
    }
  }

  /// Run complete database repair
  static Future<void> runCompleteRepair() async {
    try {
      log('Starting complete database repair...');

      await repairSalesTable();
      await repairDiscountTable();
      await repairSalesTransactionTable();

      log('Complete database repair finished successfully');
    } catch (e) {
      log('Error during complete database repair: $e');
    }
  }

  /// Check database health and report missing columns
  static Future<Map<String, List<String>>> checkDatabaseHealth() async {
    Map<String, List<String>> missingColumns = {};

    try {
      final db = await AppDatabase().database;

      // Check Sales table
      List<String> salesMissing = [];
      if (!await columnExists(db, 'Sales', 'subtotalAmount')) salesMissing.add('subtotalAmount');
      if (!await columnExists(db, 'Sales', 'cgstAmount')) salesMissing.add('cgstAmount');
      if (!await columnExists(db, 'Sales', 'sgstAmount')) salesMissing.add('sgstAmount');
      if (!await columnExists(db, 'Sales', 'igstAmount')) salesMissing.add('igstAmount');
      if (!await columnExists(db, 'Sales', 'cessAmount')) salesMissing.add('cessAmount');
      if (!await columnExists(db, 'Sales', 'totalTaxAmount')) salesMissing.add('totalTaxAmount');
      if (!await columnExists(db, 'Sales', 'isReverseCharge')) salesMissing.add('isReverseCharge');
      if (!await columnExists(db, 'Sales', 'stateCode')) salesMissing.add('stateCode');
      if (!await columnExists(db, 'Sales', 'taxConfiguration')) salesMissing.add('taxConfiguration');
      if (salesMissing.isNotEmpty) missingColumns['Sales'] = salesMissing;

      // Check Discount table
      List<String> discountMissing = [];
      if (!await columnExists(db, 'Discount', 'priority')) discountMissing.add('priority');
      if (!await columnExists(db, 'Discount', 'discountType')) discountMissing.add('discountType');
      if (!await columnExists(db, 'Discount', 'couponCode')) discountMissing.add('couponCode');
      if (discountMissing.isNotEmpty) missingColumns['Discount'] = discountMissing;

      // Check SalesTransaction table
      List<String> salesTransactionMissing = [];
      if (!await columnExists(db, 'salesTransaction', 'baseAmount')) {
        salesTransactionMissing.add('baseAmount');
      }
      if (!await columnExists(db, 'salesTransaction', 'igst')) {
        salesTransactionMissing.add('igst');
      }
      if (!await columnExists(db, 'salesTransaction', 'gstRate')) {
        salesTransactionMissing.add('gstRate');
      }
      if (salesTransactionMissing.isNotEmpty) {
        missingColumns['salesTransaction'] = salesTransactionMissing;
      }
    } catch (e) {
      log('Error checking database health: $e');
    }

    return missingColumns;
  }

  /// Print database health report
  static Future<void> printHealthReport() async {
    try {
      final missingColumns = await checkDatabaseHealth();

      log('=== DATABASE HEALTH REPORT ===');
      if (missingColumns.isEmpty) {
        log('✅ Database is healthy - no missing columns detected');
      } else {
        log('⚠️  Missing columns detected:');
        missingColumns.forEach((table, columns) {
          log('  Table: $table');
          for (String column in columns) {
            log('    - Missing column: $column');
          }
        });
      }
      log('==============================');
    } catch (e) {
      log('Error generating health report: $e');
    }
  }
}
