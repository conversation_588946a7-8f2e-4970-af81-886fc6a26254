import '.././table_columns.dart' as db;

class InventoryDto {
  final int? id;
  String shopId;
  String? rawMId;
  String? inventoryId;
  String? inventoyProductName;
  String? inventoryQunatity;
  String? inventoryUnit;
  String? createdDate;
  String? updatedDate;
  int? status;
  int? rowStatus;

  // Convertible inventory fields
  bool? isConvertible;
  String? outputItemName;
  String? outputQuantity;
  String? outputUnit;
  String? conversionRatio;
  String? convertedStock;

  InventoryDto({
    this.id,
    required this.shopId,
    this.inventoryId,
    this.inventoyProductName,
    this.inventoryQunatity,
    this.rawMId,
    this.inventoryUnit,
    this.createdDate,
    this.updatedDate,
    this.status,
    this.rowStatus,
    this.isConvertible,
    this.outputItemName,
    this.outputQuantity,
    this.outputUnit,
    this.conversionRatio,
    this.convertedStock,
  });

  factory InventoryDto.fromJson(Map<String, dynamic> json) {
    return InventoryDto(
      id: json[db.id],
      shopId: json[db.dbShopUId],
      inventoryId: json[db.inventoryId],
      inventoyProductName: json[db.inventoyProductName],
      inventoryQunatity: json[db.inventoryQunatity],
      inventoryUnit: json[db.inventoryUnit],
      createdDate: json[db.createdDate],
      updatedDate: json[db.updatedDate],
      status: json[db.status],
      rowStatus: json[db.rowStatus],
      isConvertible: json[db.isConvertible] == 1,
      outputItemName: json[db.outputItemName],
      outputQuantity: json[db.outputQuantity],
      outputUnit: json[db.outputUnit],
      conversionRatio: json[db.conversionRatio],
      convertedStock: json[db.convertedStock],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      db.id: id,
      db.dbShopUId: shopId,
      db.inventoryId: inventoryId,
      db.inventoyProductName: inventoyProductName,
      db.inventoryQunatity: inventoryQunatity,
      db.inventoryUnit: inventoryUnit,
      db.createdDate: createdDate,
      db.updatedDate: updatedDate,
      db.status: status,
      db.rowStatus: rowStatus,
      db.isConvertible: isConvertible == true ? 1 : 0,
      db.outputItemName: outputItemName,
      db.outputQuantity: outputQuantity,
      db.outputUnit: outputUnit,
      db.conversionRatio: conversionRatio,
      db.convertedStock: convertedStock ?? '0',
    };
  }

  // Helper methods for conversion calculations
  double get rawQuantity => double.tryParse(inventoryQunatity ?? '0') ?? 0.0;
  double get convertedQuantity => double.tryParse(convertedStock ?? '0') ?? 0.0;
  double get conversionRatioValue => double.tryParse(conversionRatio ?? '1') ?? 1.0;

  /// Calculate how much converted stock can be produced from current raw stock
  double get maxConvertibleAmount {
    if (isConvertible != true) return 0.0;
    return rawQuantity * conversionRatioValue;
  }

  /// Check if this item can be converted (has convertible flag and raw stock)
  bool get canConvert => isConvertible == true && rawQuantity > 0;

  /// Get display name for converted item
  String get convertedItemDisplayName => outputItemName ?? '$inventoyProductName (Converted)';
}
