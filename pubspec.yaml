name: coffee_cofe
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

# ========================================
# CORE DEPENDENCIES
# ========================================
dependencies:
  flutter:
    sdk: flutter

  # UI Framework
  cupertino_icons: ^1.0.8
  sqflite: ^2.4.1
  shared_preferences: ^2.5.3
  path_provider: ^2.1.5
  path: ^1.9.0
  provider: ^6.1.5
  get: ^4.7.2
  get_it: ^8.0.3
  intl: ^0.19.0
  uuid: ^4.5.1
  collection: ^1.18.0
  stack_trace: ^1.11.1
  dropdown_button2: ^2.3.9
  flutter_colorpicker: ^1.1.0
  text_scroll: ^0.2.0
  popover: ^0.3.1
  pie_menu: ^3.3.0
  image_picker: ^1.1.2
  permission_handler: ^12.0.1
  device_info_plus: ^11.3.0
  flutter_local_notifications: ^19.3.0
  url_launcher: ^6.3.1
  share_plus: ^11.0.0
  dio: ^5.8.0+1
  http: ^1.4.0
  sqlite_viewer2: ^2.0.0
  device_preview_plus: ^2.1.5
  sqflite_common_ffi: ^2.3.4+4

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting and Code Quality
  flutter_lints: ^5.0.0

flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/svg/
    - assets/images/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
