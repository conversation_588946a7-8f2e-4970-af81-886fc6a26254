import 'package:flutter_test/flutter_test.dart';
import 'package:coffee_cofe/database/entities/gst_configuration_dto.dart';

void main() {
  group('GST Configuration Tests', () {
    test('GST Configuration DTO should create and serialize correctly', () {
      final config = GSTConfigurationDto(
        workSpaceId: 'test_workspace',
        businessName: 'Test Coffee Shop',
        businessGSTIN: '33AAAAA0000A1Z5',
        businessStateCode: '33',
        businessAddress: '123 Test Street, Test City',
        defaultGSTRate: 18.0,
        enableIGST: true,
        enableCess: false,
        enableReverseCharge: false,
        taxInclusivePricing: false,
        roundingMethod: 'ROUND_OFF',
        isActive: true,
        syncStatus: 0,
      );

      // Test basic properties
      expect(config.businessName, equals('Test Coffee Shop'));
      expect(config.businessGSTIN, equals('33AAAAA0000A1Z5'));
      expect(config.defaultGSTRate, equals(18.0));
      expect(config.enableIGST, isTrue);
      expect(config.enableCess, isFalse);
      expect(config.taxInclusivePricing, isFalse);
      expect(config.roundingMethod, equals('ROUND_OFF'));

      // Test serialization
      final map = config.toMap();
      expect(map['businessName'], equals('Test Coffee Shop'));
      expect(map['defaultGSTRate'], equals(18.0));
      expect(map['enableIGST'], equals(1)); // Boolean stored as int
      expect(map['enableCess'], equals(0));

      // Test deserialization
      final configFromMap = GSTConfigurationDto.fromMap(map);
      expect(configFromMap.businessName, equals(config.businessName));
      expect(configFromMap.defaultGSTRate, equals(config.defaultGSTRate));
      expect(configFromMap.enableIGST, equals(config.enableIGST));
      expect(configFromMap.enableCess, equals(config.enableCess));
    });

    test('GST Configuration copyWith should work correctly', () {
      final originalConfig = GSTConfigurationDto(
        workSpaceId: 'test_workspace',
        businessName: 'Original Coffee Shop',
        businessGSTIN: '33AAAAA0000A1Z5',
        businessStateCode: '33',
        businessAddress: '123 Test Street',
        defaultGSTRate: 18.0,
        enableIGST: true,
        enableCess: false,
        enableReverseCharge: false,
        taxInclusivePricing: false,
        roundingMethod: 'ROUND_OFF',
        isActive: true,
        syncStatus: 0,
      );

      // Test copyWith functionality
      final updatedConfig = originalConfig.copyWith(
        businessName: 'Updated Coffee Shop',
        defaultGSTRate: 12.0,
        enableCess: true,
      );

      expect(updatedConfig.businessName, equals('Updated Coffee Shop'));
      expect(updatedConfig.defaultGSTRate, equals(12.0));
      expect(updatedConfig.enableCess, isTrue);

      // Unchanged properties should remain the same
      expect(updatedConfig.workSpaceId, equals(originalConfig.workSpaceId));
      expect(updatedConfig.businessGSTIN, equals(originalConfig.businessGSTIN));
      expect(updatedConfig.enableIGST, equals(originalConfig.enableIGST));
    });

    test('GST Configuration validation should work', () {
      // Valid configuration
      final validConfig = GSTConfigurationDto(
        workSpaceId: 'test_workspace',
        businessName: 'Test Coffee Shop',
        businessStateCode: '33',
        defaultGSTRate: 18.0,
        enableIGST: true,
        enableCess: false,
        enableReverseCharge: false,
        taxInclusivePricing: false,
        roundingMethod: 'ROUND_OFF',
        isActive: true,
        syncStatus: 0,
      );

      expect(validConfig.businessName.isNotEmpty, isTrue);
      expect(validConfig.defaultGSTRate, greaterThan(0));
      expect(validConfig.defaultGSTRate, lessThanOrEqualTo(100));
      expect(validConfig.businessStateCode.isNotEmpty, isTrue);
      expect(['ROUND_OFF', 'ROUND_UP', 'ROUND_DOWN'].contains(validConfig.roundingMethod), isTrue);
    });
  });
}
