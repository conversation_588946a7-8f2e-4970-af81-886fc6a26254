import 'package:flutter_test/flutter_test.dart';
import 'package:coffee_cofe/services/mock_auth_service.dart';
import 'package:coffee_cofe/config/app_config.dart';

void main() {
  group('Demo Credentials Tests', () {
    test('Mock auth service should be enabled in development', () {
      expect(AppConfig.useMockAuth, isTrue);
      expect(AppConfig.showMockUserList, isTrue);
      expect(AppConfig.isDevelopment, isTrue);
    });

    test('All demo users should have valid credentials', () async {
      // Test Manager credentials
      final managerLogin = await MockAuthService.login(
        identifier: '<EMAIL>',
        password: 'manager123',
      );
      expect(managerLogin.success, isTrue);
      expect(managerLogin.data?.response?.role, equals('manager'));

      // Test Owner credentials
      final ownerLogin = await MockAuthService.login(
        identifier: '<EMAIL>',
        password: 'owner2024',
      );
      expect(ownerLogin.success, isTrue);
      expect(ownerLogin.data?.response?.role, equals('admin'));

      // Test Barista credentials
      final baristaLogin = await MockAuthService.login(
        identifier: '<EMAIL>',
        password: 'barista123',
      );
      expect(baristaLogin.success, isTrue);
      expect(baristaLogin.data?.response?.role, equals('user'));

      // Test Cashier credentials
      final cashierLogin = await MockAuthService.login(
        identifier: '<EMAIL>',
        password: 'cashier123',
      );
      expect(cashierLogin.success, isTrue);
      expect(cashierLogin.data?.response?.role, equals('user'));

      // Test Demo user credentials
      final demoLogin = await MockAuthService.login(
        identifier: '<EMAIL>',
        password: 'demo123',
      );
      expect(demoLogin.success, isTrue);
      expect(demoLogin.data?.response?.role, equals('user'));

      // Test simple test user credentials
      final testLogin = await MockAuthService.login(
        identifier: '<EMAIL>',
        password: 'test',
      );
      expect(testLogin.success, isTrue);
      expect(testLogin.data?.response?.role, equals('user'));
    });

    test('Mobile number login should work for all demo users', () async {
      // Test Manager mobile login
      final managerMobileLogin = await MockAuthService.login(
        identifier: '9876543210',
        password: 'manager123',
      );
      expect(managerMobileLogin.success, isTrue);
      expect(managerMobileLogin.data?.response?.userName, equals('Sarah Johnson'));

      // Test Owner mobile login
      final ownerMobileLogin = await MockAuthService.login(
        identifier: '9123456789',
        password: 'owner2024',
      );
      expect(ownerMobileLogin.success, isTrue);
      expect(ownerMobileLogin.data?.response?.userName, equals('Michael Chen'));

      // Test Demo user mobile login
      final demoMobileLogin = await MockAuthService.login(
        identifier: '9000000000',
        password: 'demo123',
      );
      expect(demoMobileLogin.success, isTrue);
      expect(demoMobileLogin.data?.response?.userName, equals('Demo User'));
    });

    test('Invalid credentials should fail appropriately', () async {
      // Test invalid email
      final invalidEmailLogin = await MockAuthService.login(
        identifier: '<EMAIL>',
        password: 'wrongpassword',
      );
      expect(invalidEmailLogin.success, isFalse);
      expect(invalidEmailLogin.message, contains('Invalid credentials'));

      // Test invalid password
      final invalidPasswordLogin = await MockAuthService.login(
        identifier: '<EMAIL>',
        password: 'wrongpassword',
      );
      expect(invalidPasswordLogin.success, isFalse);
      expect(invalidPasswordLogin.message, contains('Invalid credentials'));
    });

    test('All demo users should have workspace information', () async {
      final demoLogin = await MockAuthService.login(
        identifier: '<EMAIL>',
        password: 'demo123',
      );
      
      expect(demoLogin.success, isTrue);
      expect(demoLogin.data?.response?.workspaceId, isNotNull);
      expect(demoLogin.data?.response?.workspaceId, equals('coffee_shop_001'));
      expect(demoLogin.data?.response?.workspaceName, equals('Coffee Shop Workspace'));
    });

    test('Demo users should have proper token information', () async {
      final managerLogin = await MockAuthService.login(
        identifier: '<EMAIL>',
        password: 'manager123',
      );
      
      expect(managerLogin.success, isTrue);
      expect(managerLogin.data?.response?.token, isNotNull);
      expect(managerLogin.data?.response?.token, startsWith('mock_token_'));
    });
  });
}
