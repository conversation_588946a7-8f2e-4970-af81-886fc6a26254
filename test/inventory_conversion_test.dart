import 'package:flutter_test/flutter_test.dart';
import 'package:coffee_cofe/database/entities/inventory_category_dto.dart';
import 'package:coffee_cofe/services/inventory_conversion_service.dart';

void main() {
  group('Inventory Conversion Tests', () {
    test('InventoryDto should create convertible inventory correctly', () {
      final inventory = InventoryDto(
        shopId: 'test_shop',
        inventoryId: 'inv_001',
        inventoyProductName: 'Coffee Powder',
        inventoryQunatity: '1000', // 1000g
        inventoryUnit: 'gram',
        isConvertible: true,
        outputItemName: 'Coffee Decoction',
        outputQuantity: '10',
        outputUnit: 'liter',
        conversionRatio: '0.01', // 1g → 0.01L
        convertedStock: '5', // 5L already converted
      );

      // Test basic properties
      expect(inventory.isConvertible, isTrue);
      expect(inventory.outputItemName, equals('Coffee Decoction'));
      expect(inventory.rawQuantity, equals(1000.0));
      expect(inventory.convertedQuantity, equals(5.0));
      expect(inventory.conversionRatioValue, equals(0.01));
      expect(inventory.canConvert, isTrue);
    });

    test('InventoryDto should calculate conversion amounts correctly', () {
      final inventory = InventoryDto(
        shopId: 'test_shop',
        inventoryId: 'inv_002',
        inventoyProductName: 'Milk Powder',
        inventoryQunatity: '500', // 500g
        inventoryUnit: 'gram',
        isConvertible: true,
        outputItemName: 'Milk',
        outputQuantity: '5',
        outputUnit: 'liter',
        conversionRatio: '0.01', // 1g → 0.01L
        convertedStock: '0',
      );

      // Test max convertible amount
      expect(inventory.maxConvertibleAmount, equals(5.0)); // 500g * 0.01 = 5L

      // Test conversion display name
      expect(inventory.convertedItemDisplayName, equals('Milk'));
    });

    test('InventoryDto should handle non-convertible inventory', () {
      final inventory = InventoryDto(
        shopId: 'test_shop',
        inventoryId: 'inv_003',
        inventoyProductName: 'Sugar',
        inventoryQunatity: '100',
        inventoryUnit: 'gram',
        isConvertible: false,
      );

      expect(inventory.isConvertible, isFalse);
      expect(inventory.canConvert, isFalse);
      expect(inventory.maxConvertibleAmount, equals(0.0));
      expect(inventory.convertedItemDisplayName, equals('Sugar (Converted)'));
    });

    test('InventoryDto should serialize and deserialize correctly', () {
      final originalInventory = InventoryDto(
        shopId: 'test_shop',
        inventoryId: 'inv_004',
        inventoyProductName: 'Tea Leaves',
        inventoryQunatity: '200',
        inventoryUnit: 'gram',
        isConvertible: true,
        outputItemName: 'Tea Decoction',
        outputQuantity: '2',
        outputUnit: 'liter',
        conversionRatio: '0.01',
        convertedStock: '1.5',
        status: 1,
        rowStatus: 1,
      );

      // Test serialization
      final json = originalInventory.toJson();
      expect(json['inventoyProductName'], equals('Tea Leaves'));
      expect(json['isConvertible'], equals(1)); // Boolean stored as int
      expect(json['outputItemName'], equals('Tea Decoction'));
      expect(json['conversionRatio'], equals('0.01'));
      expect(json['convertedStock'], equals('1.5'));

      // Test deserialization
      final deserializedInventory = InventoryDto.fromJson(json);
      expect(deserializedInventory.inventoyProductName, equals(originalInventory.inventoyProductName));
      expect(deserializedInventory.isConvertible, equals(originalInventory.isConvertible));
      expect(deserializedInventory.outputItemName, equals(originalInventory.outputItemName));
      expect(deserializedInventory.conversionRatio, equals(originalInventory.conversionRatio));
      expect(deserializedInventory.convertedStock, equals(originalInventory.convertedStock));
    });

    test('InventoryDto should handle edge cases correctly', () {
      // Test with zero raw quantity
      final zeroRawInventory = InventoryDto(
        shopId: 'test_shop',
        inventoryId: 'inv_005',
        inventoyProductName: 'Empty Stock',
        inventoryQunatity: '0',
        inventoryUnit: 'gram',
        isConvertible: true,
        outputItemName: 'Output',
        conversionRatio: '0.01',
        convertedStock: '0',
      );

      expect(zeroRawInventory.canConvert, isFalse); // Can't convert with zero raw stock
      expect(zeroRawInventory.maxConvertibleAmount, equals(0.0));

      // Test with null/empty conversion ratio
      final noRatioInventory = InventoryDto(
        shopId: 'test_shop',
        inventoryId: 'inv_006',
        inventoyProductName: 'No Ratio',
        inventoryQunatity: '100',
        inventoryUnit: 'gram',
        isConvertible: true,
        outputItemName: 'Output',
        conversionRatio: null,
        convertedStock: '0',
      );

      expect(noRatioInventory.conversionRatioValue, equals(1.0)); // Default ratio
      expect(noRatioInventory.maxConvertibleAmount, equals(100.0)); // 100 * 1.0

      // Test with invalid conversion ratio
      final invalidRatioInventory = InventoryDto(
        shopId: 'test_shop',
        inventoryId: 'inv_007',
        inventoyProductName: 'Invalid Ratio',
        inventoryQunatity: '100',
        inventoryUnit: 'gram',
        isConvertible: true,
        outputItemName: 'Output',
        conversionRatio: 'invalid',
        convertedStock: '0',
      );

      expect(invalidRatioInventory.conversionRatioValue, equals(1.0)); // Default to 1.0 for invalid
    });

    test('InventoryDto should handle null values gracefully', () {
      final inventory = InventoryDto(
        shopId: 'test_shop',
        inventoryId: 'inv_008',
        inventoyProductName: 'Null Test',
        inventoryQunatity: null,
        inventoryUnit: 'gram',
        isConvertible: null,
        outputItemName: null,
        conversionRatio: null,
        convertedStock: null,
      );

      expect(inventory.rawQuantity, equals(0.0));
      expect(inventory.convertedQuantity, equals(0.0));
      expect(inventory.conversionRatioValue, equals(1.0));
      expect(inventory.isConvertible, isNull);
      expect(inventory.canConvert, isFalse);
      expect(inventory.maxConvertibleAmount, equals(0.0));
    });

    test('InventoryDto should validate conversion scenarios', () {
      // Scenario 1: Coffee powder to decoction
      final coffeePowder = InventoryDto(
        shopId: 'coffee_shop',
        inventoryId: 'coffee_001',
        inventoyProductName: 'Premium Coffee Powder',
        inventoryQunatity: '2000', // 2kg
        inventoryUnit: 'gram',
        isConvertible: true,
        outputItemName: 'Coffee Decoction',
        outputQuantity: '20',
        outputUnit: 'liter',
        conversionRatio: '0.01', // 100g → 1L
        convertedStock: '0',
      );

      expect(coffeePowder.maxConvertibleAmount, equals(20.0)); // 2000g * 0.01 = 20L
      expect(coffeePowder.canConvert, isTrue);

      // Scenario 2: Milk powder to liquid milk
      final milkPowder = InventoryDto(
        shopId: 'coffee_shop',
        inventoryId: 'milk_001',
        inventoyProductName: 'Milk Powder',
        inventoryQunatity: '1000', // 1kg
        inventoryUnit: 'gram',
        isConvertible: true,
        outputItemName: 'Liquid Milk',
        outputQuantity: '8',
        outputUnit: 'liter',
        conversionRatio: '0.008', // 125g → 1L
        convertedStock: '2', // 2L already converted
      );

      expect(milkPowder.maxConvertibleAmount, equals(8.0)); // 1000g * 0.008 = 8L
      expect(milkPowder.convertedQuantity, equals(2.0));
      expect(milkPowder.canConvert, isTrue);

      // Scenario 3: Tea leaves to tea decoction
      final teaLeaves = InventoryDto(
        shopId: 'coffee_shop',
        inventoryId: 'tea_001',
        inventoyProductName: 'Premium Tea Leaves',
        inventoryQunatity: '500', // 500g
        inventoryUnit: 'gram',
        isConvertible: true,
        outputItemName: 'Tea Decoction',
        outputQuantity: '25',
        outputUnit: 'liter',
        conversionRatio: '0.05', // 20g → 1L
        convertedStock: '10', // 10L already converted
      );

      expect(teaLeaves.maxConvertibleAmount, equals(25.0)); // 500g * 0.05 = 25L
      expect(teaLeaves.convertedQuantity, equals(10.0));
      expect(teaLeaves.canConvert, isTrue);
    });

    test('InventoryDto should handle conversion with different units', () {
      // Test with different unit combinations
      final inventory = InventoryDto(
        shopId: 'test_shop',
        inventoryId: 'unit_test',
        inventoyProductName: 'Multi-Unit Test',
        inventoryQunatity: '5', // 5 kg
        inventoryUnit: 'kg',
        isConvertible: true,
        outputItemName: 'Converted Product',
        outputQuantity: '50',
        outputUnit: 'liter',
        conversionRatio: '10', // 1kg → 10L
        convertedStock: '15', // 15L already converted
      );

      expect(inventory.rawQuantity, equals(5.0));
      expect(inventory.convertedQuantity, equals(15.0));
      expect(inventory.conversionRatioValue, equals(10.0));
      expect(inventory.maxConvertibleAmount, equals(50.0)); // 5kg * 10 = 50L
      expect(inventory.canConvert, isTrue);
    });
  });

  group('Inventory Conversion Service Tests', () {
    test('Should validate conversion correctly', () {
      final inventory = InventoryDto(
        shopId: 'test_shop',
        inventoryId: 'inv_001',
        inventoyProductName: 'Coffee Powder',
        inventoryQunatity: '1000',
        inventoryUnit: 'gram',
        isConvertible: true,
        outputItemName: 'Coffee Decoction',
        conversionRatio: '0.01',
        convertedStock: '0',
      );

      // Valid conversion
      final validResult = InventoryConversionService.validateConversion(
        inventory: inventory,
        amountToConvert: 500.0,
      );
      expect(validResult.isValid, isTrue);
      expect(validResult.errorMessage, isNull);

      // Invalid amount (zero)
      final zeroAmountResult = InventoryConversionService.validateConversion(
        inventory: inventory,
        amountToConvert: 0.0,
      );
      expect(zeroAmountResult.isValid, isFalse);
      expect(zeroAmountResult.errorCode, equals(ConversionErrorCode.invalidAmount));

      // Insufficient stock
      final insufficientResult = InventoryConversionService.validateConversion(
        inventory: inventory,
        amountToConvert: 1500.0,
      );
      expect(insufficientResult.isValid, isFalse);
      expect(insufficientResult.errorCode, equals(ConversionErrorCode.insufficientStock));
    });

    test('Should calculate conversion correctly', () {
      final inventory = InventoryDto(
        shopId: 'test_shop',
        inventoryId: 'inv_002',
        inventoyProductName: 'Milk Powder',
        inventoryQunatity: '1000',
        inventoryUnit: 'gram',
        isConvertible: true,
        outputItemName: 'Liquid Milk',
        conversionRatio: '0.008',
        convertedStock: '2',
      );

      final result = InventoryConversionService.calculateConversion(
        inventory: inventory,
        amountToConvert: 250.0,
      );

      expect(result.isValid, isTrue);
      expect(result.rawAmountToConvert, equals(250.0));
      expect(result.convertedAmountProduced, equals(2.0)); // 250 * 0.008
      expect(result.newRawQuantity, equals(750.0)); // 1000 - 250
      expect(result.newConvertedQuantity, equals(4.0)); // 2 + 2
    });

    test('Should create updated inventory correctly', () {
      final originalInventory = InventoryDto(
        shopId: 'test_shop',
        inventoryId: 'inv_003',
        inventoyProductName: 'Tea Leaves',
        inventoryQunatity: '500',
        inventoryUnit: 'gram',
        isConvertible: true,
        outputItemName: 'Tea Decoction',
        conversionRatio: '0.05',
        convertedStock: '5',
      );

      final calculationResult = InventoryConversionService.calculateConversion(
        inventory: originalInventory,
        amountToConvert: 100.0,
      );

      final updatedInventory = InventoryConversionService.createUpdatedInventory(
        originalInventory: originalInventory,
        calculationResult: calculationResult,
      );

      expect(updatedInventory.inventoryQunatity, equals('400.0')); // 500 - 100
      expect(updatedInventory.convertedStock, equals('10.0')); // 5 + 5 (100 * 0.05)
      expect(updatedInventory.inventoyProductName, equals(originalInventory.inventoyProductName));
      expect(updatedInventory.isConvertible, equals(originalInventory.isConvertible));
    });

    test('Should validate conversion setup correctly', () {
      // Valid setup
      final validSetup = InventoryConversionService.validateConversionSetup(
        outputItemName: 'Coffee Decoction',
        outputQuantity: '10',
        outputUnit: 'liter',
        conversionRatio: '0.01',
      );
      expect(validSetup.isValid, isTrue);
      expect(validSetup.errors, isEmpty);

      // Invalid setup - empty fields
      final invalidSetup = InventoryConversionService.validateConversionSetup(
        outputItemName: '',
        outputQuantity: '',
        outputUnit: null,
        conversionRatio: '',
      );
      expect(invalidSetup.isValid, isFalse);
      expect(invalidSetup.errors.length, equals(4));

      // Invalid setup - invalid numbers
      final invalidNumbers = InventoryConversionService.validateConversionSetup(
        outputItemName: 'Test Product',
        outputQuantity: 'invalid',
        outputUnit: 'liter',
        conversionRatio: '-1',
      );
      expect(invalidNumbers.isValid, isFalse);
      expect(invalidNumbers.errors.length, equals(2));
    });

    test('Should generate conversion summary correctly', () {
      final inventory = InventoryDto(
        shopId: 'test_shop',
        inventoryId: 'inv_004',
        inventoyProductName: 'Coffee Powder',
        inventoryQunatity: '1000',
        inventoryUnit: 'gram',
        isConvertible: true,
        outputItemName: 'Coffee Decoction',
        conversionRatio: '0.01',
        convertedStock: '0',
      );

      final summary = InventoryConversionService.getConversionSummary(
        inventory: inventory,
        amountToConvert: 200.0,
      );

      expect(summary, contains('Converting 200.00 gram'));
      expect(summary, contains('Coffee Powder'));
      expect(summary, contains('2.00'));
      expect(summary, contains('Coffee Decoction'));
    });
  });
}
