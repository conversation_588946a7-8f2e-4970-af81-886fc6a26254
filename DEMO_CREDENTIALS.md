# Demo Credentials Guide

This document provides comprehensive information about the demo credentials available in the Coffee Shop POS system for development and testing purposes.

## 🚀 Quick Access

For immediate testing, use these simplified credentials:

| User Type | Email/Mobile | Password | Role |
|-----------|-------------|----------|------|
| **Demo User** | `<EMAIL>` or `9000000000` | `demo123` | user |
| **Test User** | `<EMAIL>` or `1111111111` | `test` | user |

## 👥 Complete Demo User List

### 1. Store Manager - <PERSON>
- **Email**: `<EMAIL>`
- **Mobile**: `9876543210`
- **Password**: `manager123`
- **Role**: `manager`
- **Department**: Management
- **Access Level**: Full system access including reports, settings, and user management

### 2. Store Owner - <PERSON> Chen
- **Email**: `<EMAIL>`
- **Mobile**: `9123456789`
- **Password**: `owner2024`
- **Role**: `admin`
- **Department**: Administration
- **Access Level**: Complete administrative access to all features

### 3. Bari<PERSON> - <PERSON> Rodriguez
- **Email**: `<EMAIL>`
- **Mobile**: `9234567890`
- **Password**: `barista123`
- **Role**: `user`
- **Department**: Operations
- **Access Level**: Product management, inventory, and sales operations

### 4. Cashier - David Kumar
- **Email**: `<EMAIL>`
- **Mobile**: `9345678901`
- **Password**: `cashier123`
- **Role**: `user`
- **Department**: Sales
- **Access Level**: Billing, sales transactions, and basic reporting

### 5. Demo User
- **Email**: `<EMAIL>`
- **Mobile**: `9000000000`
- **Password**: `demo123`
- **Role**: `user`
- **Department**: Testing
- **Access Level**: Standard user access for demonstration purposes

### 6. Test User
- **Email**: `<EMAIL>`
- **Mobile**: `1111111111`
- **Password**: `test`
- **Role**: `user`
- **Department**: Testing
- **Access Level**: Basic testing access with simple credentials

## 🔧 How to Use Demo Credentials

### Method 1: Auto-Fill from Demo Screen
1. Open the login screen
2. Look for the "Demo Users (Development Mode)" section
3. Tap on any user card to automatically fill the login form
4. Click "Sign In" to login

### Method 2: Quick Access Buttons
1. Use the "Quick Access" section for instant credential filling
2. Click "Demo User" or "Test User" buttons for immediate access

### Method 3: Manual Entry
1. Enter any email or mobile number from the list above
2. Enter the corresponding password
3. Click "Sign In"

## ⚙️ Configuration

Demo credentials are controlled by these settings in `lib/config/app_config.dart`:

```dart
static const bool useMockAuth = true; // Enable/disable mock authentication
static const bool showMockUserList = true; // Show/hide demo user list
static const String environment = 'development'; // Environment setting
```

## 🧪 Testing Features

All demo credentials have been thoroughly tested and include:

- ✅ Email-based login
- ✅ Mobile number-based login
- ✅ Proper role assignment
- ✅ Workspace information
- ✅ Authentication tokens
- ✅ Error handling for invalid credentials

## 🔒 Security Notes

- **Development Only**: These credentials are for development and testing only
- **Mock Data**: All user data is simulated and stored locally
- **No Real API**: Authentication bypasses real API calls when `useMockAuth = true`
- **Easy Replacement**: Set `useMockAuth = false` to switch to real authentication

## 🚨 Production Deployment

Before deploying to production:

1. Set `useMockAuth = false` in `app_config.dart`
2. Set `environment = 'production'`
3. Configure real API endpoints
4. Remove or hide demo user interface elements

## 📱 Workspace Information

All demo users belong to the same workspace:
- **Workspace ID**: `coffee_shop_001`
- **Workspace Name**: `Coffee Shop Workspace`
- **Features**: Full POS functionality including billing, inventory, reports, and settings

## 🔄 Automatic Testing

Demo credentials are automatically tested in the test suite:
- Run `flutter test test/demo_credentials_test.dart` to verify all credentials
- Tests cover login functionality, role assignment, and workspace data
- All tests must pass before deployment

## 📞 Support

If you encounter issues with demo credentials:
1. Check that `AppConfig.useMockAuth` is set to `true`
2. Verify the environment is set to `development`
3. Run the demo credentials test suite
4. Check the console for any authentication errors

---

**Note**: This demo system provides a complete authentication experience that can be easily replaced with real API integration when ready for production deployment.
